using System;
using System.Collections.Generic;

namespace SimpleAccounting
{
    /// <summary>
    /// نموذج العميل البسيط
    /// </summary>
    public class SimpleCustomer
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Phone { get; set; } = "";
        public string Email { get; set; } = "";
        public string Address { get; set; } = "";
        public decimal Balance { get; set; } = 0;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// نموذج المورد البسيط
    /// </summary>
    public class SimpleSupplier
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Company { get; set; } = "";
        public string ContactPerson { get; set; } = "";
        public string Phone { get; set; } = "";
        public string Email { get; set; } = "";
        public string Address { get; set; } = "";
        public string TaxNumber { get; set; } = "";
        public decimal Balance { get; set; } = 0;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// نموذج المنتج البسيط
    /// </summary>
    public class SimpleProduct
    {
        public int Id { get; set; }
        public string Code { get; set; } = "";
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public string Barcode { get; set; } = "";
        public string Category { get; set; } = "";
        public int SupplierId { get; set; } = 0;
        public decimal Price { get; set; } = 0; // سعر البيع
        public decimal PurchasePrice { get; set; } = 0;
        public int Quantity { get; set; } = 0; // الكمية الحالية
        public int MinQuantity { get; set; } = 0; // الحد الأدنى
        public int MaxQuantity { get; set; } = 0; // الحد الأقصى
        public string Unit { get; set; } = "قطعة";
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime? LastMovementDate { get; set; }
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// حساب هامش الربح
        /// </summary>
        public decimal ProfitMargin => PurchasePrice > 0 ? ((Price - PurchasePrice) / PurchasePrice) * 100 : 0;

        /// <summary>
        /// حساب إجمالي قيمة المخزون
        /// </summary>
        public decimal TotalStockValue => Quantity * PurchasePrice;

        /// <summary>
        /// حساب إجمالي المبيعات (يحتاج لحساب من الفواتير)
        /// </summary>
        public decimal TotalSales { get; set; } = 0;
    }

    /// <summary>
    /// نموذج الفاتورة البسيط
    /// </summary>
    public class SimpleInvoice
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; } = "";
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = "";
        public DateTime Date { get; set; } = DateTime.Now;
        public decimal SubTotal { get; set; } = 0;
        public decimal TaxAmount { get; set; } = 0;
        public decimal DiscountAmount { get; set; } = 0;
        public decimal Total { get; set; } = 0;
        public string Status { get; set; } = "مدفوعة";
        public string PaymentMethod { get; set; } = "نقدي";
        public string Notes { get; set; } = "";
        public List<SimpleInvoiceItem> Items { get; set; } = new List<SimpleInvoiceItem>();
        public DateTime CreatedDate { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// نموذج عنصر الفاتورة البسيط
    /// </summary>
    public class SimpleInvoiceItem
    {
        public int Id { get; set; }
        public int InvoiceId { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; } = "";
        public int Quantity { get; set; } = 1;
        public decimal Price { get; set; } = 0;
        public decimal DiscountPercent { get; set; } = 0;
        public decimal Total { get; set; } = 0;
    }

    /// <summary>
    /// نموذج إعدادات النظام البسيط
    /// </summary>
    public class SimpleSettings
    {
        public string CompanyName { get; set; } = "شركة المحاسبة الشاملة";
        public string CompanyAddress { get; set; } = "";
        public string CompanyPhone { get; set; } = "";
        public string CompanyEmail { get; set; } = "";
        public string CompanyLogo { get; set; } = "";
        public decimal TaxRate { get; set; } = 15;
        public string Currency { get; set; } = "ريال سعودي";
        public int LastInvoiceNumber { get; set; } = 0;
        public int LastCustomerId { get; set; } = 0;
        public int LastSupplierId { get; set; } = 0;
        public int LastProductId { get; set; } = 0;
        public int LastStockMovementId { get; set; } = 0;
        public DateTime LastBackupDate { get; set; } = DateTime.Now;
        public bool AutoBackup { get; set; } = true;
        public int BackupRetentionDays { get; set; } = 30;
    }

    /// <summary>
    /// نموذج حركة المخزون البسيط
    /// </summary>
    public class SimpleStockMovement
    {
        public int Id { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; } = "";
        public string MovementType { get; set; } = ""; // "دخول" أو "خروج" أو "تسوية"
        public int Quantity { get; set; } = 0;
        public decimal UnitPrice { get; set; } = 0;
        public string Reference { get; set; } = ""; // رقم الفاتورة أو المرجع
        public string Notes { get; set; } = "";
        public DateTime MovementDate { get; set; } = DateTime.Now;
        public string CreatedBy { get; set; } = "";

        /// <summary>
        /// حساب إجمالي قيمة الحركة
        /// </summary>
        public decimal TotalValue => Quantity * UnitPrice;
    }

    /// <summary>
    /// نموذج المدفوعات البسيط
    /// </summary>
    public class SimplePayment
    {
        public int Id { get; set; }
        public string PaymentNumber { get; set; } = "";
        public int InvoiceId { get; set; }
        public string InvoiceNumber { get; set; } = "";
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = "";
        public decimal Amount { get; set; } = 0;
        public string PaymentMethod { get; set; } = "نقدي";
        public DateTime PaymentDate { get; set; } = DateTime.Now;
        public string Status { get; set; } = "مكتمل";
        public string Notes { get; set; } = "";
        public DateTime CreatedDate { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// نموذج التقرير البسيط
    /// </summary>
    public class SimpleReport
    {
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public DateTime GeneratedDate { get; set; } = DateTime.Now;
        public string GeneratedBy { get; set; } = "";
        public Dictionary<string, object> Data { get; set; } = new Dictionary<string, object>();
        public List<string> Columns { get; set; } = new List<string>();
        public List<Dictionary<string, object>> Rows { get; set; } = new List<Dictionary<string, object>>();
    }

    /// <summary>
    /// نموذج الفئة البسيط
    /// </summary>
    public class SimpleCategory
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public string Color { get; set; } = "#0078D4";
        public string Icon { get; set; } = "📦";
        public int ProductCount { get; set; } = 0;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// نموذج المستخدم البسيط
    /// </summary>
    public class SimpleUser
    {
        public int Id { get; set; }
        public string Username { get; set; } = "";
        public string PasswordHash { get; set; } = "";
        public string FullName { get; set; } = "";
        public string Email { get; set; } = "";
        public string Role { get; set; } = "User";
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime? LastLoginDate { get; set; }
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// نموذج النسخة الاحتياطية البسيط
    /// </summary>
    public class SimpleBackup
    {
        public int Id { get; set; }
        public string FileName { get; set; } = "";
        public string FilePath { get; set; } = "";
        public long FileSize { get; set; } = 0;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string CreatedBy { get; set; } = "";
        public string Description { get; set; } = "";
        public bool IsAutomatic { get; set; } = false;
    }
}
