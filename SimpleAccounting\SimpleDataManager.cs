using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;

namespace SimpleAccounting
{
    /// <summary>
    /// مدير البيانات البسيط - بدون قاعدة بيانات SQL
    /// يستخدم ملفات JSON لحفظ البيانات
    /// </summary>
    public class SimpleDataManager
    {
        private static SimpleDataManager _instance;
        private static readonly object _lock = new object();
        private readonly string _dataPath;

        private SimpleDataManager()
        {
            _dataPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
            EnsureDataDirectoryExists();
        }

        public static SimpleDataManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new SimpleDataManager();
                    }
                }
                return _instance;
            }
        }

        private void EnsureDataDirectoryExists()
        {
            if (!Directory.Exists(_dataPath))
            {
                Directory.CreateDirectory(_dataPath);
            }
        }

        #region Generic Data Operations

        /// <summary>
        /// حفظ البيانات في ملف JSON
        /// </summary>
        public void SaveData<T>(string fileName, T data)
        {
            try
            {
                var filePath = Path.Combine(_dataPath, $"{fileName}.json");
                var json = JsonSerializer.Serialize(data, new JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });
                File.WriteAllText(filePath, json);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل البيانات من ملف JSON
        /// </summary>
        public T LoadData<T>(string fileName) where T : new()
        {
            try
            {
                var filePath = Path.Combine(_dataPath, $"{fileName}.json");
                if (!File.Exists(filePath))
                {
                    return new T();
                }

                var json = File.ReadAllText(filePath);
                return JsonSerializer.Deserialize<T>(json) ?? new T();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحميل البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من وجود ملف البيانات
        /// </summary>
        public bool DataExists(string fileName)
        {
            var filePath = Path.Combine(_dataPath, $"{fileName}.json");
            return File.Exists(filePath);
        }

        /// <summary>
        /// حذف ملف البيانات
        /// </summary>
        public void DeleteData(string fileName)
        {
            try
            {
                var filePath = Path.Combine(_dataPath, $"{fileName}.json");
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف البيانات: {ex.Message}");
            }
        }

        #endregion

        #region Sample Data Generation

        /// <summary>
        /// إنشاء بيانات تجريبية
        /// </summary>
        public void GenerateSampleData()
        {
            try
            {
                // إنشاء عملاء تجريبيين
                var customers = new List<SimpleCustomer>
                {
                    new SimpleCustomer { Id = 1, Name = "أحمد محمد", Phone = "0501234567", Email = "<EMAIL>", Address = "الرياض" },
                    new SimpleCustomer { Id = 2, Name = "فاطمة علي", Phone = "0509876543", Email = "<EMAIL>", Address = "جدة" },
                    new SimpleCustomer { Id = 3, Name = "محمد سالم", Phone = "0505555555", Email = "<EMAIL>", Address = "الدمام" },
                    new SimpleCustomer { Id = 4, Name = "نورا أحمد", Phone = "0507777777", Email = "<EMAIL>", Address = "مكة" },
                    new SimpleCustomer { Id = 5, Name = "خالد عبدالله", Phone = "0508888888", Email = "<EMAIL>", Address = "المدينة" }
                };
                SaveData("customers", customers);

                // إنشاء موردين تجريبيين
                var suppliers = new List<SimpleSupplier>
                {
                    new SimpleSupplier { Id = 1, Name = "شركة التقنية المتقدمة", Phone = "0112345678", Email = "<EMAIL>", Address = "الرياض" },
                    new SimpleSupplier { Id = 2, Name = "مؤسسة الإلكترونيات", Phone = "0123456789", Email = "<EMAIL>", Address = "جدة" },
                    new SimpleSupplier { Id = 3, Name = "شركة المواد الغذائية", Phone = "0134567890", Email = "<EMAIL>", Address = "الدمام" }
                };
                SaveData("suppliers", suppliers);

                // إنشاء منتجات تجريبية
                var products = new List<SimpleProduct>
                {
                    new SimpleProduct { Id = 1, Name = "لابتوب ديل", Code = "DELL001", Price = 2500, Quantity = 10, Category = "إلكترونيات" },
                    new SimpleProduct { Id = 2, Name = "ماوس لاسلكي", Code = "MOUSE001", Price = 50, Quantity = 25, Category = "إلكترونيات" },
                    new SimpleProduct { Id = 3, Name = "كيبورد ميكانيكي", Code = "KB001", Price = 150, Quantity = 15, Category = "إلكترونيات" },
                    new SimpleProduct { Id = 4, Name = "شاشة 24 بوصة", Code = "MON001", Price = 800, Quantity = 8, Category = "إلكترونيات" },
                    new SimpleProduct { Id = 5, Name = "طابعة ليزر", Code = "PRINT001", Price = 600, Quantity = 5, Category = "إلكترونيات" },
                    new SimpleProduct { Id = 6, Name = "كتاب المحاسبة", Code = "BOOK001", Price = 80, Quantity = 20, Category = "كتب" },
                    new SimpleProduct { Id = 7, Name = "قلم حبر جاف", Code = "PEN001", Price = 5, Quantity = 100, Category = "قرطاسية" },
                    new SimpleProduct { Id = 8, Name = "دفتر ملاحظات", Code = "NOTE001", Price = 15, Quantity = 50, Category = "قرطاسية" }
                };
                SaveData("products", products);

                // إنشاء فواتير تجريبية
                var invoices = new List<SimpleInvoice>
                {
                    new SimpleInvoice 
                    { 
                        Id = 1, 
                        InvoiceNumber = "INV-001", 
                        CustomerId = 1, 
                        CustomerName = "أحمد محمد",
                        Date = DateTime.Now.AddDays(-5), 
                        Total = 2550,
                        Items = new List<SimpleInvoiceItem>
                        {
                            new SimpleInvoiceItem { ProductId = 1, ProductName = "لابتوب ديل", Quantity = 1, Price = 2500, Total = 2500 },
                            new SimpleInvoiceItem { ProductId = 2, ProductName = "ماوس لاسلكي", Quantity = 1, Price = 50, Total = 50 }
                        }
                    },
                    new SimpleInvoice 
                    { 
                        Id = 2, 
                        InvoiceNumber = "INV-002", 
                        CustomerId = 2, 
                        CustomerName = "فاطمة علي",
                        Date = DateTime.Now.AddDays(-3), 
                        Total = 950,
                        Items = new List<SimpleInvoiceItem>
                        {
                            new SimpleInvoiceItem { ProductId = 4, ProductName = "شاشة 24 بوصة", Quantity = 1, Price = 800, Total = 800 },
                            new SimpleInvoiceItem { ProductId = 3, ProductName = "كيبورد ميكانيكي", Quantity = 1, Price = 150, Total = 150 }
                        }
                    }
                };
                SaveData("invoices", invoices);

                // إنشاء إعدادات النظام
                var settings = new SimpleSettings
                {
                    CompanyName = "شركة المحاسبة الشاملة",
                    CompanyAddress = "الرياض، المملكة العربية السعودية",
                    CompanyPhone = "0112345678",
                    CompanyEmail = "<EMAIL>",
                    TaxRate = 15,
                    Currency = "ريال سعودي",
                    LastInvoiceNumber = 2,
                    LastCustomerId = 5,
                    LastSupplierId = 3,
                    LastProductId = 8
                };
                SaveData("settings", settings);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء البيانات التجريبية: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من وجود بيانات تجريبية
        /// </summary>
        public bool HasSampleData()
        {
            return DataExists("customers") && DataExists("products") && DataExists("settings");
        }

        #endregion

        #region Data Access Methods

        /// <summary>
        /// الحصول على جميع العملاء
        /// </summary>
        public List<SimpleCustomer> GetAllCustomers()
        {
            return LoadData<List<SimpleCustomer>>("customers");
        }

        /// <summary>
        /// الحصول على جميع الموردين
        /// </summary>
        public List<SimpleSupplier> GetAllSuppliers()
        {
            return LoadData<List<SimpleSupplier>>("suppliers");
        }

        /// <summary>
        /// الحصول على جميع المنتجات
        /// </summary>
        public List<SimpleProduct> GetAllProducts()
        {
            return LoadData<List<SimpleProduct>>("products");
        }

        /// <summary>
        /// الحصول على جميع الفواتير
        /// </summary>
        public List<SimpleInvoice> GetAllInvoices()
        {
            return LoadData<List<SimpleInvoice>>("invoices");
        }

        /// <summary>
        /// الحصول على إعدادات النظام
        /// </summary>
        public SimpleSettings GetSettings()
        {
            return LoadData<SimpleSettings>("settings");
        }

        #endregion

        #region Customer Management

        public SimpleCustomer GetCustomerById(int customerId)
        {
            try
            {
                var customers = GetAllCustomers();
                return customers.FirstOrDefault(c => c.Id == customerId);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث عن العميل: {ex.Message}");
            }
        }

        public void DeleteCustomer(int customerId)
        {
            try
            {
                var customers = GetAllCustomers();
                var customerToDelete = customers.FirstOrDefault(c => c.Id == customerId);

                if (customerToDelete != null)
                {
                    customers.Remove(customerToDelete);
                    SaveData("customers", customers);
                }
                else
                {
                    throw new Exception("العميل غير موجود");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف العميل: {ex.Message}");
            }
        }

        public void AddCustomer(SimpleCustomer customer)
        {
            try
            {
                var customers = GetAllCustomers();

                // تحديد معرف جديد
                customer.Id = customers.Count > 0 ? customers.Max(c => c.Id) + 1 : 1;

                customers.Add(customer);
                SaveData("customers", customers);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة العميل: {ex.Message}");
            }
        }

        public void UpdateCustomer(SimpleCustomer customer)
        {
            try
            {
                var customers = GetAllCustomers();
                var existingCustomerIndex = customers.FindIndex(c => c.Id == customer.Id);

                if (existingCustomerIndex >= 0)
                {
                    customers[existingCustomerIndex] = customer;
                    SaveData("customers", customers);
                }
                else
                {
                    throw new Exception("العميل غير موجود");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث العميل: {ex.Message}");
            }
        }

        #endregion
    }
}
