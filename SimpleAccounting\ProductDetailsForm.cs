using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;

namespace SimpleAccounting
{
    public partial class ProductDetailsForm : Form
    {
        private SimpleProduct _product;
        private TabControl mainTabControl;
        private Panel headerPanel;
        private Panel summaryCardsPanel;
        private Panel buttonsPanel;

        // تبويب المعلومات الأساسية
        private TabPage basicInfoTab;
        private TextBox txtName;
        private TextBox txtDescription;
        private TextBox txtBarcode;
        private ComboBox cmbCategory;
        private ComboBox cmbSupplier;
        private NumericUpDown numPurchasePrice;
        private NumericUpDown numSalePrice;
        private NumericUpDown numCurrentStock;
        private NumericUpDown numMinStock;
        private NumericUpDown numMaxStock;
        private TextBox txtUnit;

        // تبويب حركات المخزون
        private TabPage stockMovementsTab;
        private DataGridView stockMovementsGrid;
        private Panel stockStatsPanel;

        // تبويب الإحصائيات والتقارير
        private TabPage statisticsTab;
        private Panel chartPanel;
        private DataGridView salesHistoryGrid;

        // عناصر الرأس والملخص
        private Label lblProductName;
        private Label lblProductCode;
        private Label lblSupplierName;
        private Label lblCurrentStock;
        private Label lblPurchasePrice;
        private Label lblSalePrice;
        private Label lblTotalSales;
        private Label lblLastMovement;

        // أزرار الإجراءات
        private Button btnSave;
        private Button btnClose;
        private Button btnAddCategory;
        private Button btnPrintReport;
        private Button btnExportData;

        public ProductDetailsForm(int productId)
        {
            LoadProductData(productId);
            InitializeComponent();
            SetupModernUI();
            LoadProductStockMovements();
            LoadSalesHistory();
        }

        private void LoadProductData(int productId)
        {
            try
            {
                _product = SimpleDataManager.Instance.GetProductById(productId);
                if (_product == null)
                {
                    throw new Exception("المنتج غير موجود");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.DialogResult = DialogResult.Cancel;
            }
        }

        private void InitializeComponent()
        {
            this.Text = $"تفاصيل المنتج - {_product?.Name ?? "غير محدد"}";
            this.Size = new Size(1400, 900);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MinimumSize = new Size(1200, 800);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(240, 244, 248);

            // إنشاء لوحة الرأس
            headerPanel = CreateHeaderPanel();
            headerPanel.Dock = DockStyle.Top;
            headerPanel.Height = 120;

            // إنشاء لوحة البطاقات الملخصة
            summaryCardsPanel = CreateSummaryCardsPanel();
            summaryCardsPanel.Dock = DockStyle.Top;
            summaryCardsPanel.Height = 140;

            // إنشاء التبويبات الرئيسية
            mainTabControl = CreateMainTabControl();
            mainTabControl.Dock = DockStyle.Fill;

            // إنشاء لوحة الأزرار
            buttonsPanel = CreateButtonsPanel();
            buttonsPanel.Dock = DockStyle.Bottom;
            buttonsPanel.Height = 70;

            this.Controls.AddRange(new Control[] {
                buttonsPanel,      // الأزرار في الأسفل
                mainTabControl,    // التبويبات في الوسط
                summaryCardsPanel, // البطاقات الملخصة
                headerPanel        // الرأس في الأعلى
            });

            // تحميل البيانات
            LoadProductData();
        }

        private void LoadProductData()
        {
            try
            {
                // تحميل حركات المخزون
                LoadProductStockMovements();

                // تحميل تاريخ المبيعات
                LoadProductSalesHistory();

                // تحديث البطاقات الملخصة
                UpdateSummaryCards();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateSummaryCards()
        {
            // تحديث البطاقات الملخصة بالقيم الجديدة
            if (lblCurrentStock != null)
                lblCurrentStock.Text = $"{_product.Quantity} {_product.Unit}";

            if (lblPurchasePrice != null)
                lblPurchasePrice.Text = $"{_product.PurchasePrice:N2} ريال";

            if (lblSalePrice != null)
                lblSalePrice.Text = $"{_product.Price:N2} ريال";

            if (lblTotalSales != null)
                lblTotalSales.Text = $"{CalculateTotalSales():N2} ريال";
        }

        private void SetupModernUI()
        {
            // تطبيق التصميم الحديث
            this.Font = new Font("Tahoma", 9F);

            // إضافة تأثيرات بصرية للنافذة
            this.Paint += (s, e) => {
                var graphics = e.Graphics;
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                // رسم حد ملون للنافذة
                using (var pen = new Pen(Color.FromArgb(52, 152, 219), 2))
                {
                    graphics.DrawRectangle(pen, 1, 1, this.Width - 3, this.Height - 3);
                }
            };
        }

        private Panel CreateHeaderPanel()
        {
            var panel = new Panel
            {
                BackColor = Color.White,
                Padding = new Padding(20, 15, 20, 15)
            };

            // إضافة تأثيرات ثلاثية الأبعاد
            panel.Paint += (s, e) => {
                var graphics = e.Graphics;
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                // رسم ظل
                using (var shadowBrush = new SolidBrush(Color.FromArgb(20, 0, 0, 0)))
                {
                    graphics.FillRectangle(shadowBrush, 3, 3, panel.Width - 3, panel.Height - 3);
                }

                // رسم خلفية متدرجة
                using (var brush = new LinearGradientBrush(
                    panel.ClientRectangle,
                    Color.White,
                    Color.FromArgb(248, 250, 252),
                    LinearGradientMode.Vertical))
                {
                    graphics.FillRectangle(brush, panel.ClientRectangle);
                }

                // رسم حد
                using (var pen = new Pen(Color.FromArgb(220, 223, 230), 1))
                {
                    graphics.DrawRectangle(pen, 0, 0, panel.Width - 1, panel.Height - 1);
                }
            };

            // أيقونة المنتج
            var productIcon = new Label
            {
                Text = "📦",
                Font = new Font("Segoe UI Emoji", 32F),
                ForeColor = Color.FromArgb(52, 152, 219),
                Size = new Size(60, 60),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // اسم المنتج
            lblProductName = new Label
            {
                Text = _product?.Name ?? "غير محدد",
                Font = new Font("Tahoma", 20F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(90, 20),
                Size = new Size(400, 35),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // كود المنتج
            lblProductCode = new Label
            {
                Text = $"الكود: {_product?.Code ?? "غير محدد"}",
                Font = new Font("Tahoma", 12F),
                ForeColor = Color.FromArgb(127, 140, 141),
                Location = new Point(90, 55),
                Size = new Size(200, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // اسم المورد
            var supplierName = GetSupplierName();
            lblSupplierName = new Label
            {
                Text = $"المورد: {supplierName}",
                Font = new Font("Tahoma", 12F),
                ForeColor = Color.FromArgb(127, 140, 141),
                Location = new Point(300, 55),
                Size = new Size(250, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            panel.Controls.AddRange(new Control[] {
                productIcon, lblProductName, lblProductCode, lblSupplierName
            });

            return panel;
        }

        private Panel CreateSummaryCardsPanel()
        {
            var panel = new Panel
            {
                BackColor = Color.FromArgb(240, 244, 248),
                Padding = new Padding(20, 10, 20, 10),
                AutoScroll = true,
                RightToLeft = RightToLeft.Yes
            };

            // حساب القيم المالية والإحصائيات
            var totalSales = CalculateTotalSales();
            var profitMargin = _product.Price - _product.PurchasePrice;
            var stockValue = _product.Quantity * _product.PurchasePrice;

            // إنشاء FlowLayoutPanel لترتيب البطاقات تلقائياً
            var flowPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.RightToLeft, // تغيير الاتجاه للعربية
                WrapContents = true,
                AutoScroll = true,
                RightToLeft = RightToLeft.Yes,
                Padding = new Padding(10)
            };

            // حساب عرض البطاقة
            int cardWidth = 180;
            int cardHeight = 100;

            // بطاقة الكمية المتاحة
            var stockCard = CreateSummaryCard("الكمية المتاحة", $"{_product.Quantity} {_product.Unit}", "📦",
                GetStockStatusColor(), Point.Empty, new Size(cardWidth, cardHeight));

            // بطاقة سعر الشراء
            var purchasePriceCard = CreateSummaryCard("سعر الشراء", $"{_product.PurchasePrice:N2} ريال", "💰",
                Color.FromArgb(155, 89, 182), Point.Empty, new Size(cardWidth, cardHeight));

            // بطاقة سعر البيع
            var salePriceCard = CreateSummaryCard("سعر البيع", $"{_product.Price:N2} ريال", "🏷️",
                Color.FromArgb(52, 152, 219), Point.Empty, new Size(cardWidth, cardHeight));

            // بطاقة هامش الربح
            var profitCard = CreateSummaryCard("هامش الربح", $"{profitMargin:N2} ريال", "📈",
                profitMargin >= 0 ? Color.FromArgb(46, 204, 113) : Color.FromArgb(231, 76, 60),
                Point.Empty, new Size(cardWidth, cardHeight));

            // بطاقة قيمة المخزون
            var stockValueCard = CreateSummaryCard("قيمة المخزون", $"{stockValue:N2} ريال", "💎",
                Color.FromArgb(230, 126, 34), Point.Empty, new Size(cardWidth, cardHeight));

            // بطاقة إجمالي المبيعات
            var totalSalesCard = CreateSummaryCard("إجمالي المبيعات", $"{totalSales:N2} ريال", "💵",
                Color.FromArgb(26, 188, 156), Point.Empty, new Size(cardWidth, cardHeight));

            flowPanel.Controls.AddRange(new Control[] {
                stockCard, purchasePriceCard, salePriceCard, profitCard, stockValueCard, totalSalesCard
            });

            panel.Controls.Add(flowPanel);

            // حفظ المراجع للتحديث لاحقاً
            lblCurrentStock = stockCard.Controls.OfType<Label>().LastOrDefault();
            lblPurchasePrice = purchasePriceCard.Controls.OfType<Label>().LastOrDefault();
            lblSalePrice = salePriceCard.Controls.OfType<Label>().LastOrDefault();
            lblTotalSales = totalSalesCard.Controls.OfType<Label>().LastOrDefault();

            return panel;
        }

        private Color GetStockStatusColor()
        {
            if (_product.Quantity <= 0)
                return Color.FromArgb(231, 76, 60); // أحمر - نفد المخزون
            else if (_product.Quantity <= _product.MinQuantity)
                return Color.FromArgb(230, 126, 34); // برتقالي - أقل من الحد الأدنى
            else
                return Color.FromArgb(46, 204, 113); // أخضر - متوفر
        }

        private TabControl CreateMainTabControl()
        {
            var tabControl = new TabControl
            {
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true,
                Appearance = TabAppearance.FlatButtons,
                SizeMode = TabSizeMode.Fixed,
                ItemSize = new Size(150, 35)
            };

            // تنسيق التبويبات
            tabControl.DrawMode = TabDrawMode.OwnerDrawFixed;
            tabControl.DrawItem += (s, e) => {
                var tab = tabControl.TabPages[e.Index];
                var tabRect = tabControl.GetTabRect(e.Index);
                var graphics = e.Graphics;
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                // لون الخلفية
                var backColor = e.State == DrawItemState.Selected ?
                    Color.FromArgb(52, 152, 219) : Color.FromArgb(240, 244, 248);
                var textColor = e.State == DrawItemState.Selected ?
                    Color.White : Color.FromArgb(52, 73, 94);

                // رسم الخلفية
                using (var brush = new SolidBrush(backColor))
                {
                    graphics.FillRectangle(brush, tabRect);
                }

                // رسم النص
                using (var brush = new SolidBrush(textColor))
                {
                    var stringFormat = new StringFormat
                    {
                        Alignment = StringAlignment.Center,
                        LineAlignment = StringAlignment.Center
                    };
                    graphics.DrawString(tab.Text, tabControl.Font, brush, tabRect, stringFormat);
                }

                // رسم حد
                using (var pen = new Pen(Color.FromArgb(220, 223, 230), 1))
                {
                    graphics.DrawRectangle(pen, tabRect);
                }
            };

            // إنشاء التبويبات
            basicInfoTab = CreateBasicInfoTab();
            stockMovementsTab = CreateStockMovementsTab();
            statisticsTab = CreateStatisticsTab();

            tabControl.TabPages.AddRange(new TabPage[] {
                basicInfoTab, stockMovementsTab, statisticsTab
            });

            return tabControl;
        }

        private TabPage CreateBasicInfoTab()
        {
            var tab = new TabPage("📝 المعلومات الأساسية")
            {
                BackColor = Color.White,
                Padding = new Padding(20),
                RightToLeft = RightToLeft.Yes
            };

            var scrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                BackColor = Color.White
            };

            // ترتيب الأقسام من اليمين لليسار (مناسب للعربية)
            // قسم الأسعار والكميات - على اليمين
            var pricesGroup = CreateGroupBox("الأسعار والكميات", 20, 20, 600, 200);
            CreatePricesAndQuantitiesFields(pricesGroup);

            // قسم المعلومات الأساسية - على اليسار
            var basicInfoGroup = CreateGroupBox("المعلومات الأساسية", 640, 20, 600, 200);
            CreateBasicInfoFields(basicInfoGroup);

            // قسم إعدادات المخزون - على اليمين
            var stockGroup = CreateGroupBox("إعدادات المخزون", 20, 240, 600, 120);
            CreateStockSettingsFields(stockGroup);

            // قسم معلومات المورد - على اليسار
            var supplierGroup = CreateGroupBox("معلومات المورد", 640, 240, 600, 120);
            CreateSupplierFields(supplierGroup);

            scrollPanel.Controls.AddRange(new Control[] {
                pricesGroup, basicInfoGroup, stockGroup, supplierGroup
            });

            tab.Controls.Add(scrollPanel);
            return tab;
        }

        private GroupBox CreateGroupBox(string title, int x, int y, int width, int height)
        {
            var groupBox = new GroupBox
            {
                Text = title,
                Font = new Font("Tahoma", 11F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(x, y),
                Size = new Size(width, height),
                Padding = new Padding(15, 25, 15, 15),
                RightToLeft = RightToLeft.Yes
            };

            // إضافة تأثيرات بصرية
            groupBox.Paint += (s, e) => {
                var graphics = e.Graphics;
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                // رسم ظل خفيف
                using (var shadowBrush = new SolidBrush(Color.FromArgb(10, 0, 0, 0)))
                {
                    graphics.FillRectangle(shadowBrush, 2, 2, groupBox.Width - 2, groupBox.Height - 2);
                }

                // رسم حد ملون
                using (var pen = new Pen(Color.FromArgb(52, 152, 219), 2))
                {
                    graphics.DrawRectangle(pen, 0, 0, groupBox.Width - 1, groupBox.Height - 1);
                }
            };

            return groupBox;
        }

        private void CreateBasicInfoFields(GroupBox parent)
        {
            // ترتيب الحقول من اليمين لليسار (مناسب للعربية)

            // الوصف - على اليمين
            var descLabel = CreateLabel("الوصف:", 380, 35);
            txtDescription = CreateTextBox(_product?.Description ?? "", 480, 32, 100);

            // اسم المنتج - على اليسار
            var nameLabel = CreateLabel("اسم المنتج:", 20, 35);
            txtName = CreateTextBox(_product?.Name ?? "", 120, 32, 200);

            // الوحدة - على اليمين
            var unitLabel = CreateLabel("الوحدة:", 380, 75);
            txtUnit = CreateTextBox(_product?.Unit ?? "قطعة", 480, 72, 100);

            // الباركود - على اليسار
            var barcodeLabel = CreateLabel("الباركود:", 20, 75);
            txtBarcode = CreateTextBox(_product?.Barcode ?? "", 120, 72, 200);

            // زر إضافة تصنيف - على اليمين
            btnAddCategory = new Button
            {
                Text = "➕",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                Size = new Size(30, 25),
                Location = new Point(550, 112),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
            };
            btnAddCategory.FlatAppearance.BorderSize = 0;
            btnAddCategory.Click += BtnAddCategory_Click;

            // التصنيف - على اليسار
            var categoryLabel = CreateLabel("التصنيف:", 20, 115);
            cmbCategory = CreateCategoryComboBox(120, 112, 200);

            parent.Controls.AddRange(new Control[] {
                descLabel, txtDescription, nameLabel, txtName,
                unitLabel, txtUnit, barcodeLabel, txtBarcode,
                btnAddCategory, categoryLabel, cmbCategory
            });
        }

        private void CreatePricesAndQuantitiesFields(GroupBox parent)
        {
            // ترتيب الحقول من اليمين لليسار (مناسب للعربية)

            // سعر البيع - على اليمين
            var salePriceLabel = CreateLabel("سعر البيع:", 380, 35);
            numSalePrice = CreateNumericUpDown(_product?.Price ?? 0, 480, 32, 100);

            // سعر الشراء - على اليسار
            var purchasePriceLabel = CreateLabel("سعر الشراء:", 20, 35);
            numPurchasePrice = CreateNumericUpDown(_product?.PurchasePrice ?? 0, 120, 32, 120);

            // الحد الأدنى - على اليمين
            var minStockLabel = CreateLabel("الحد الأدنى:", 380, 75);
            numMinStock = CreateNumericUpDown(_product?.MinQuantity ?? 0, 480, 72, 100, 0);

            // الكمية الحالية - على اليسار
            var currentStockLabel = CreateLabel("الكمية الحالية:", 20, 75);
            numCurrentStock = CreateNumericUpDown(_product?.Quantity ?? 0, 120, 72, 120, 0);

            // الحد الأقصى - في الوسط
            var maxStockLabel = CreateLabel("الحد الأقصى:", 260, 115);
            numMaxStock = CreateNumericUpDown(_product?.MaxQuantity ?? 0, 360, 112, 100, 0);

            parent.Controls.AddRange(new Control[] {
                salePriceLabel, numSalePrice, purchasePriceLabel, numPurchasePrice,
                minStockLabel, numMinStock, currentStockLabel, numCurrentStock,
                maxStockLabel, numMaxStock
            });
        }

        private void CreateSupplierFields(GroupBox parent)
        {
            var supplierLabel = CreateLabel("المورد:", 20, 35);
            cmbSupplier = CreateSupplierComboBox(120, 32, 300);

            var supplierInfoLabel = CreateLabel("معلومات المورد:", 20, 75);
            var supplierInfoText = new Label
            {
                Text = GetSupplierInfo(),
                Font = new Font("Tahoma", 9F),
                ForeColor = Color.FromArgb(127, 140, 141),
                Location = new Point(120, 75),
                Size = new Size(400, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            parent.Controls.AddRange(new Control[] {
                supplierLabel, cmbSupplier, supplierInfoLabel, supplierInfoText
            });
        }

        private void CreateStockSettingsFields(GroupBox parent)
        {
            var reorderLabel = CreateLabel("نقطة إعادة الطلب:", 20, 35);
            var reorderValue = new Label
            {
                Text = $"{_product?.MinQuantity ?? 0} {_product?.Unit ?? "قطعة"}",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(230, 126, 34),
                Location = new Point(140, 35),
                Size = new Size(150, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var stockStatusLabel = CreateLabel("حالة المخزون:", 20, 75);
            var stockStatusValue = new Label
            {
                Text = GetStockStatusText(),
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = GetStockStatusColor(),
                Location = new Point(120, 75),
                Size = new Size(200, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            parent.Controls.AddRange(new Control[] {
                reorderLabel, reorderValue, stockStatusLabel, stockStatusValue
            });
        }

        // دوال مساعدة لإنشاء العناصر
        private Label CreateLabel(string text, int x, int y)
        {
            return new Label
            {
                Text = text,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(x, y),
                Size = new Size(90, 25),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };
        }

        private TextBox CreateTextBox(string text, int x, int y, int width)
        {
            return new TextBox
            {
                Text = text,
                Font = new Font("Tahoma", 10F),
                Location = new Point(x, y),
                Size = new Size(width, 25),
                RightToLeft = RightToLeft.Yes
            };
        }

        private NumericUpDown CreateNumericUpDown(decimal value, int x, int y, int width, int decimalPlaces = 2)
        {
            return new NumericUpDown
            {
                Value = value,
                Font = new Font("Tahoma", 10F),
                Location = new Point(x, y),
                Size = new Size(width, 25),
                DecimalPlaces = decimalPlaces,
                Maximum = 999999,
                RightToLeft = RightToLeft.Yes
            };
        }

        private ComboBox CreateCategoryComboBox(int x, int y, int width)
        {
            var combo = new ComboBox
            {
                Font = new Font("Tahoma", 10F),
                Location = new Point(x, y),
                Size = new Size(width, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes
            };
            LoadCategoriesToCombo(combo);
            return combo;
        }

        private ComboBox CreateSupplierComboBox(int x, int y, int width)
        {
            var combo = new ComboBox
            {
                Font = new Font("Tahoma", 10F),
                Location = new Point(x, y),
                Size = new Size(width, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes
            };
            LoadSuppliersToCombo(combo);
            return combo;
        }

        private TabPage CreateStockMovementsTab()
        {
            var tab = new TabPage("📊 حركات المخزون")
            {
                BackColor = Color.White,
                Padding = new Padding(20),
                RightToLeft = RightToLeft.Yes
            };

            // لوحة إحصائيات المخزون العلوية
            stockStatsPanel = CreateStockStatsPanel();
            stockStatsPanel.Dock = DockStyle.Top;
            stockStatsPanel.Height = 120;

            // جدول حركات المخزون
            stockMovementsGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                GridColor = Color.FromArgb(230, 230, 230),
                RowHeadersVisible = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                Font = new Font("Tahoma", 9F),
                RightToLeft = RightToLeft.Yes,
                Margin = new Padding(0, 10, 0, 0)
            };

            // تنسيق رأس الجدول
            stockMovementsGrid.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 152, 219);
            stockMovementsGrid.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            stockMovementsGrid.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            stockMovementsGrid.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            stockMovementsGrid.ColumnHeadersHeight = 40;

            // إضافة الأعمدة
            stockMovementsGrid.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "MovementType", HeaderText = "نوع الحركة", FillWeight = 12 },
                new DataGridViewTextBoxColumn { Name = "Quantity", HeaderText = "الكمية", FillWeight = 10 },
                new DataGridViewTextBoxColumn { Name = "UnitPrice", HeaderText = "سعر الوحدة", FillWeight = 12 },
                new DataGridViewTextBoxColumn { Name = "TotalValue", HeaderText = "القيمة الإجمالية", FillWeight = 15 },
                new DataGridViewTextBoxColumn { Name = "Reference", HeaderText = "المرجع", FillWeight = 15 },
                new DataGridViewTextBoxColumn { Name = "Date", HeaderText = "التاريخ والوقت", FillWeight = 18 },
                new DataGridViewTextBoxColumn { Name = "Notes", HeaderText = "ملاحظات", FillWeight = 18 }
            });

            tab.Controls.AddRange(new Control[] {
                stockMovementsGrid, stockStatsPanel
            });

            // تحميل بيانات تاريخ المبيعات
            LoadProductSalesHistory();

            return tab;
        }

        private void LoadProductSalesHistory()
        {
            try
            {
                var invoices = SimpleDataManager.Instance.GetAllInvoices();
                salesHistoryGrid.Rows.Clear();

                foreach (var invoice in invoices)
                {
                    var productItems = invoice.Items.Where(item => item.ProductId == _product.Id);

                    foreach (var item in productItems)
                    {
                        var customer = SimpleDataManager.Instance.GetCustomerById(invoice.CustomerId);
                        var row = salesHistoryGrid.Rows[salesHistoryGrid.Rows.Add()];

                        row.Cells["InvoiceNumber"].Value = invoice.InvoiceNumber;
                        row.Cells["CustomerName"].Value = customer?.Name ?? "عميل غير محدد";
                        row.Cells["Quantity"].Value = $"{item.Quantity} {_product.Unit}";
                        row.Cells["UnitPrice"].Value = $"{item.Price:N2} ريال";
                        row.Cells["TotalAmount"].Value = $"{item.Total:N2} ريال";
                        row.Cells["SaleDate"].Value = invoice.Date.ToString("dd/MM/yyyy HH:mm");

                        // تلوين الصفوف بالتناوب
                        if (salesHistoryGrid.Rows.Count % 2 == 0)
                        {
                            row.DefaultCellStyle.BackColor = Color.FromArgb(248, 250, 252);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تاريخ المبيعات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private Panel CreateStockStatsPanel()
        {
            var panel = new Panel
            {
                BackColor = Color.FromArgb(248, 250, 252),
                Padding = new Padding(10)
            };

            var movements = SimpleDataManager.Instance.GetStockMovementsByProduct(_product.Id);
            var totalIn = movements.Where(m => m.MovementType == "دخول").Sum(m => m.Quantity);
            var totalOut = movements.Where(m => m.MovementType == "خروج").Sum(m => m.Quantity);
            var totalAdjustments = movements.Where(m => m.MovementType == "تسوية").Sum(m => m.Quantity);
            var lastMovement = movements.OrderByDescending(m => m.MovementDate).FirstOrDefault();

            // بطاقات الإحصائيات
            int cardWidth = 200;
            int cardSpacing = 20;

            var inCard = CreateStatsCard("إجمالي الدخول", $"{totalIn} {_product.Unit}", "📈",
                Color.FromArgb(46, 204, 113), new Point(10, 10), new Size(cardWidth, 80));

            var outCard = CreateStatsCard("إجمالي الخروج", $"{totalOut} {_product.Unit}", "📉",
                Color.FromArgb(231, 76, 60), new Point(cardWidth + cardSpacing + 10, 10), new Size(cardWidth, 80));

            var adjustCard = CreateStatsCard("التسويات", $"{totalAdjustments} {_product.Unit}", "⚖️",
                Color.FromArgb(230, 126, 34), new Point((cardWidth + cardSpacing) * 2 + 10, 10), new Size(cardWidth, 80));

            var lastMoveCard = CreateStatsCard("آخر حركة",
                lastMovement?.MovementDate.ToString("dd/MM/yyyy") ?? "لا توجد", "🕒",
                Color.FromArgb(52, 152, 219), new Point((cardWidth + cardSpacing) * 3 + 10, 10), new Size(cardWidth, 80));

            panel.Controls.AddRange(new Control[] {
                inCard, outCard, adjustCard, lastMoveCard
            });

            return panel;
        }

        private TabPage CreateStatisticsTab()
        {
            var tab = new TabPage("📈 الإحصائيات والتقارير")
            {
                BackColor = Color.White,
                Padding = new Padding(20),
                RightToLeft = RightToLeft.Yes
            };

            // لوحة الرسوم البيانية
            chartPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 300,
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var chartTitle = new Label
            {
                Text = "📊 تحليل المبيعات والمخزون",
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Dock = DockStyle.Top,
                Height = 40,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // إنشاء رسم بياني بسيط (يمكن تطويره لاحقاً)
            var chartPlaceholder = new Label
            {
                Text = "📈 الرسوم البيانية\n(قيد التطوير)",
                Font = new Font("Tahoma", 16F),
                ForeColor = Color.FromArgb(127, 140, 141),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            chartPanel.Controls.AddRange(new Control[] { chartPlaceholder, chartTitle });

            // جدول تاريخ المبيعات
            salesHistoryGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                GridColor = Color.FromArgb(230, 230, 230),
                RowHeadersVisible = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                Font = new Font("Tahoma", 9F),
                RightToLeft = RightToLeft.Yes,
                Margin = new Padding(0, 10, 0, 0)
            };

            // تنسيق رأس الجدول
            salesHistoryGrid.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(46, 204, 113);
            salesHistoryGrid.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            salesHistoryGrid.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            salesHistoryGrid.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            salesHistoryGrid.ColumnHeadersHeight = 40;

            // إضافة الأعمدة
            salesHistoryGrid.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "InvoiceNumber", HeaderText = "رقم الفاتورة", FillWeight = 15 },
                new DataGridViewTextBoxColumn { Name = "CustomerName", HeaderText = "العميل", FillWeight = 20 },
                new DataGridViewTextBoxColumn { Name = "Quantity", HeaderText = "الكمية المباعة", FillWeight = 15 },
                new DataGridViewTextBoxColumn { Name = "UnitPrice", HeaderText = "سعر الوحدة", FillWeight = 15 },
                new DataGridViewTextBoxColumn { Name = "TotalAmount", HeaderText = "المبلغ الإجمالي", FillWeight = 15 },
                new DataGridViewTextBoxColumn { Name = "SaleDate", HeaderText = "تاريخ البيع", FillWeight = 20 }
            });

            var salesTitle = new Label
            {
                Text = "💰 تاريخ المبيعات",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Dock = DockStyle.Top,
                Height = 30,
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes,
                Margin = new Padding(0, 10, 0, 0)
            };

            tab.Controls.AddRange(new Control[] {
                salesHistoryGrid, salesTitle, chartPanel
            });

            return tab;
        }

        private Panel CreateButtonsPanel()
        {
            var panel = new Panel
            {
                BackColor = Color.White,
                Padding = new Padding(20, 15, 20, 15),
                RightToLeft = RightToLeft.Yes
            };

            // إضافة تأثيرات ثلاثية الأبعاد
            panel.Paint += (s, e) => {
                var graphics = e.Graphics;
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                // رسم ظل
                using (var shadowBrush = new SolidBrush(Color.FromArgb(20, 0, 0, 0)))
                {
                    graphics.FillRectangle(shadowBrush, 3, 3, panel.Width - 3, panel.Height - 3);
                }

                // رسم خلفية متدرجة
                using (var brush = new LinearGradientBrush(
                    panel.ClientRectangle,
                    Color.White,
                    Color.FromArgb(248, 250, 252),
                    LinearGradientMode.Vertical))
                {
                    graphics.FillRectangle(brush, panel.ClientRectangle);
                }

                // رسم حد
                using (var pen = new Pen(Color.FromArgb(220, 223, 230), 1))
                {
                    graphics.DrawRectangle(pen, 0, 0, panel.Width - 1, panel.Height - 1);
                }
            };

            // ترتيب الأزرار من اليسار لليمين (مناسب للعربية)
            // زر الحفظ - أقصى اليسار
            btnSave = CreateActionButton("💾 حفظ التغييرات", Color.FromArgb(46, 204, 113), 20, 15);
            btnSave.Click += BtnSave_Click;

            // زر الإغلاق
            btnClose = CreateActionButton("❌ إغلاق", Color.FromArgb(231, 76, 60), 160, 15);
            btnClose.Click += (s, e) => this.Close();

            // زر طباعة التقرير
            btnPrintReport = CreateActionButton("🖨️ طباعة تقرير", Color.FromArgb(52, 152, 219), 300, 15);
            btnPrintReport.Click += BtnPrintReport_Click;

            // زر تصدير البيانات - أقصى اليمين
            btnExportData = CreateActionButton("📤 تصدير البيانات", Color.FromArgb(155, 89, 182), 440, 15);
            btnExportData.Click += BtnExportData_Click;

            panel.Controls.AddRange(new Control[] {
                btnSave, btnClose, btnPrintReport, btnExportData
            });

            return panel;
        }

        private Button CreateActionButton(string text, Color backColor, int x, int y)
        {
            var button = new Button
            {
                Text = text,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                Size = new Size(120, 35),
                Location = new Point(x, y),
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            button.FlatAppearance.BorderSize = 0;

            // إضافة تأثيرات الحوم
            button.MouseEnter += (s, e) => {
                button.BackColor = ControlPaint.Light(backColor, 0.2f);
            };
            button.MouseLeave += (s, e) => {
                button.BackColor = backColor;
            };

            return button;
        }

        private void CreateProductSummaryCards(Panel parentPanel)
        {
            try
            {
                // حساب القيم المالية
                var stockMovements = SimpleDataManager.Instance.GetStockMovementsByProduct(_product.Id);
                var totalSales = CalculateTotalSales();
                var lastMovement = stockMovements.FirstOrDefault();

                // إنشاء لوحة البطاقات مع حجم أكبر
                var cardsPanel = new Panel
                {
                    Location = new Point(20, 110),
                    Size = new Size(Math.Max(1000, parentPanel.Width - 40), 120),
                    BackColor = Color.Transparent,
                    AutoScroll = false
                };

                // حساب عرض البطاقة بناءً على المساحة المتاحة
                int cardWidth = Math.Max(180, (cardsPanel.Width - 40) / 5); // 5 بطاقات
                int cardSpacing = 10;

                // بطاقة الكمية المتاحة
                var stockCard = CreateInfoCard("الكمية المتاحة", $"{_product.Quantity} {_product.Unit}", "📦",
                    Color.FromArgb(46, 204, 113), new Point(0, 0), new Size(cardWidth, 100));

                // بطاقة سعر الشراء
                var purchasePriceCard = CreateInfoCard("سعر الشراء", $"{_product.PurchasePrice:N2} ريال", "💰",
                    Color.FromArgb(155, 89, 182), new Point(cardWidth + cardSpacing, 0), new Size(cardWidth, 100));

                // بطاقة سعر البيع
                var salePriceCard = CreateInfoCard("سعر البيع", $"{_product.Price:N2} ريال", "🏷️",
                    Color.FromArgb(52, 152, 219), new Point((cardWidth + cardSpacing) * 2, 0), new Size(cardWidth, 100));

                // بطاقة إجمالي المبيعات
                var totalSalesCard = CreateInfoCard("إجمالي المبيعات", $"{totalSales:N2} ريال", "📈",
                    Color.FromArgb(230, 126, 34), new Point((cardWidth + cardSpacing) * 3, 0), new Size(cardWidth, 100));

                // بطاقة آخر حركة مخزون
                var lastMovementText = lastMovement?.MovementDate.ToString("dd/MM/yyyy") ?? "لا توجد";
                var lastMovementCard = CreateInfoCard("آخر حركة مخزون", lastMovementText, "📊",
                    Color.FromArgb(231, 76, 60), new Point((cardWidth + cardSpacing) * 4, 0), new Size(cardWidth, 100));

                cardsPanel.Controls.AddRange(new Control[] {
                    stockCard, purchasePriceCard, salePriceCard, totalSalesCard, lastMovementCard
                });

                parentPanel.Controls.Add(cardsPanel);

                // حفظ المراجع للتحديث لاحقاً
                lblCurrentStock = stockCard.Controls.OfType<Label>().LastOrDefault();
                lblPurchasePrice = purchasePriceCard.Controls.OfType<Label>().LastOrDefault();
                lblSalePrice = salePriceCard.Controls.OfType<Label>().LastOrDefault();
                lblTotalSales = totalSalesCard.Controls.OfType<Label>().LastOrDefault();
                lblLastMovement = lastMovementCard.Controls.OfType<Label>().LastOrDefault();

                System.Diagnostics.Debug.WriteLine($"تم إنشاء {cardsPanel.Controls.Count} بطاقات للمنتج");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء البطاقات: {ex.Message}");
                MessageBox.Show($"خطأ في إنشاء البطاقات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private Panel CreateSummaryCard(string title, string value, string icon, Color color, Point location, Size size)
        {
            var card = new Panel
            {
                Size = size,
                Location = location,
                BackColor = Color.White,
                BorderStyle = BorderStyle.None
            };

            // إضافة تأثيرات ثلاثية الأبعاد
            card.Paint += (s, e) => {
                var graphics = e.Graphics;
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                // رسم ظل
                using (var shadowBrush = new SolidBrush(Color.FromArgb(30, 0, 0, 0)))
                {
                    graphics.FillRectangle(shadowBrush, 2, 2, card.Width - 2, card.Height - 2);
                }

                // رسم خلفية متدرجة
                using (var brush = new LinearGradientBrush(
                    card.ClientRectangle,
                    Color.White,
                    Color.FromArgb(250, 252, 255),
                    LinearGradientMode.Vertical))
                {
                    graphics.FillRectangle(brush, card.ClientRectangle);
                }

                // رسم حد ملون
                using (var pen = new Pen(color, 3))
                {
                    graphics.DrawLine(pen, 0, 0, card.Width, 0);
                }

                // رسم حد خارجي
                using (var pen = new Pen(Color.FromArgb(220, 223, 230), 1))
                {
                    graphics.DrawRectangle(pen, 0, 0, card.Width - 1, card.Height - 1);
                }
            };

            // الأيقونة - في الجانب الأيمن للغة العربية
            var iconLabel = new Label
            {
                Text = icon,
                Font = new Font("Segoe UI Emoji", 18F),
                ForeColor = color,
                Size = new Size(50, 50),
                Location = new Point(size.Width - 60, 15),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // عنوان البطاقة - محاذاة يمين
            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Tahoma", 9F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Size = new Size(size.Width - 70, 25),
                Location = new Point(10, 15),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            // قيمة البطاقة - محاذاة يمين
            var valueLabel = new Label
            {
                Text = value,
                Font = new Font("Tahoma", 11F, FontStyle.Bold),
                ForeColor = color,
                Size = new Size(size.Width - 70, 30),
                Location = new Point(10, 45),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            card.Controls.AddRange(new Control[] {
                iconLabel, titleLabel, valueLabel
            });

            return card;
        }

        private Panel CreateStatsCard(string title, string value, string icon, Color color, Point location, Size size)
        {
            var card = new Panel
            {
                Size = size,
                Location = location,
                BackColor = Color.White,
                BorderStyle = BorderStyle.None
            };

            // إضافة تأثيرات ثلاثية الأبعاد
            card.Paint += (s, e) => {
                var graphics = e.Graphics;
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                // رسم ظل
                using (var shadowBrush = new SolidBrush(Color.FromArgb(20, 0, 0, 0)))
                {
                    graphics.FillRectangle(shadowBrush, 2, 2, card.Width - 2, card.Height - 2);
                }

                // رسم خلفية متدرجة
                using (var brush = new LinearGradientBrush(
                    card.ClientRectangle,
                    Color.White,
                    Color.FromArgb(248, 250, 252),
                    LinearGradientMode.Vertical))
                {
                    graphics.FillRectangle(brush, card.ClientRectangle);
                }

                // رسم حد ملون
                using (var pen = new Pen(color, 2))
                {
                    graphics.DrawRectangle(pen, 0, 0, card.Width - 1, card.Height - 1);
                }
            };

            // الأيقونة - في الجانب الأيمن للغة العربية
            var iconLabel = new Label
            {
                Text = icon,
                Font = new Font("Segoe UI Emoji", 14F),
                ForeColor = color,
                Size = new Size(40, 40),
                Location = new Point(size.Width - 50, 10),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // عنوان البطاقة - محاذاة يمين
            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Tahoma", 8F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Size = new Size(size.Width - 60, 20),
                Location = new Point(10, 15),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            // قيمة البطاقة - محاذاة يمين
            var valueLabel = new Label
            {
                Text = value,
                Font = new Font("Tahoma", 9F, FontStyle.Bold),
                ForeColor = color,
                Size = new Size(size.Width - 60, 25),
                Location = new Point(10, 40),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            card.Controls.AddRange(new Control[] {
                iconLabel, titleLabel, valueLabel
            });

            return card;
        }

        private Panel CreateInfoCard(string title, string value, string icon, Color color, Point location, Size? customSize = null)
        {
            var cardSize = customSize ?? new Size(180, 80);
            var card = new Panel
            {
                Size = cardSize,
                Location = location,
                BackColor = Color.White,
                BorderStyle = BorderStyle.None
            };

            // إضافة تأثيرات ثلاثية الأبعاد
            card.Paint += (s, e) => {
                var graphics = e.Graphics;
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                // رسم ظل
                using (var shadowBrush = new SolidBrush(Color.FromArgb(30, 0, 0, 0)))
                {
                    graphics.FillRectangle(shadowBrush, 2, 2, card.Width - 2, card.Height - 2);
                }

                // رسم خلفية متدرجة
                using (var brush = new LinearGradientBrush(
                    card.ClientRectangle,
                    Color.White,
                    Color.FromArgb(250, 252, 255),
                    LinearGradientMode.Vertical))
                {
                    graphics.FillRectangle(brush, card.ClientRectangle);
                }

                // رسم حد ملون
                using (var pen = new Pen(color, 3))
                {
                    graphics.DrawLine(pen, 0, 0, card.Width, 0);
                }

                // رسم حد خارجي
                using (var pen = new Pen(Color.FromArgb(220, 223, 230), 1))
                {
                    graphics.DrawRectangle(pen, 0, 0, card.Width - 1, card.Height - 1);
                }
            };

            // الأيقونة - في الجانب الأيمن للغة العربية
            var iconLabel = new Label
            {
                Text = icon,
                Font = new Font("Segoe UI Emoji", 18F),
                ForeColor = color,
                Size = new Size(50, 50),
                Location = new Point(cardSize.Width - 60, 15),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // عنوان البطاقة - محاذاة يمين
            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Tahoma", 9F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Size = new Size(cardSize.Width - 70, 25),
                Location = new Point(10, 15),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            // قيمة البطاقة - محاذاة يمين
            var valueLabel = new Label
            {
                Text = value,
                Font = new Font("Tahoma", 11F, FontStyle.Bold),
                ForeColor = color,
                Size = new Size(cardSize.Width - 70, 30),
                Location = new Point(10, 45),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            card.Controls.AddRange(new Control[] {
                iconLabel, titleLabel, valueLabel
            });

            return card;
        }

        private void CreateEditingFields(Panel parentPanel)
        {
            // حقول التعديل - تحت البطاقات
            var fieldsY = 250;

            // الصف الأول: الاسم والوصف
            var nameLabel = new Label
            {
                Text = "الاسم:",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, fieldsY),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            txtName = new TextBox
            {
                Text = _product?.Name ?? "",
                Font = new Font("Tahoma", 10F),
                Location = new Point(110, fieldsY - 2),
                Size = new Size(200, 25),
                RightToLeft = RightToLeft.Yes
            };

            var descriptionLabel = new Label
            {
                Text = "الوصف:",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(330, fieldsY),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            txtDescription = new TextBox
            {
                Text = _product?.Description ?? "",
                Font = new Font("Tahoma", 10F),
                Location = new Point(420, fieldsY - 2),
                Size = new Size(200, 25),
                RightToLeft = RightToLeft.Yes
            };

            fieldsY += 40;

            // الصف الثاني: الباركود والفئة
            var barcodeLabel = new Label
            {
                Text = "الباركود:",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, fieldsY),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            txtBarcode = new TextBox
            {
                Text = _product?.Barcode ?? "",
                Font = new Font("Tahoma", 10F),
                Location = new Point(110, fieldsY - 2),
                Size = new Size(200, 25),
                RightToLeft = RightToLeft.Yes
            };

            var categoryLabel = new Label
            {
                Text = "الفئة:",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(330, fieldsY),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // هذا الكود تم نقله إلى CreateBasicInfoFields

            fieldsY += 40;

            // الصف الثالث: المورد وأسعار
            var supplierLabel = new Label
            {
                Text = "المورد:",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, fieldsY),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            cmbSupplier = new ComboBox
            {
                Font = new Font("Tahoma", 10F),
                Location = new Point(110, fieldsY - 2),
                Size = new Size(200, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes
            };
            LoadSuppliersToCombo();

            var purchasePriceLabel = new Label
            {
                Text = "سعر الشراء:",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(330, fieldsY),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            numPurchasePrice = new NumericUpDown
            {
                Value = _product?.PurchasePrice ?? 0,
                Font = new Font("Tahoma", 10F),
                Location = new Point(420, fieldsY - 2),
                Size = new Size(100, 25),
                DecimalPlaces = 2,
                Maximum = 999999,
                RightToLeft = RightToLeft.Yes
            };

            var salePriceLabel = new Label
            {
                Text = "سعر البيع:",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(540, fieldsY),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            numSalePrice = new NumericUpDown
            {
                Value = _product?.Price ?? 0,
                Font = new Font("Tahoma", 10F),
                Location = new Point(630, fieldsY - 2),
                Size = new Size(100, 25),
                DecimalPlaces = 2,
                Maximum = 999999,
                RightToLeft = RightToLeft.Yes
            };

            fieldsY += 40;

            // الصف الرابع: كميات المخزون
            var currentStockLabel = new Label
            {
                Text = "الكمية الحالية:",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, fieldsY),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            numCurrentStock = new NumericUpDown
            {
                Value = _product?.Quantity ?? 0,
                Font = new Font("Tahoma", 10F),
                Location = new Point(110, fieldsY - 2),
                Size = new Size(100, 25),
                Maximum = 999999,
                RightToLeft = RightToLeft.Yes
            };

            var minStockLabel = new Label
            {
                Text = "الحد الأدنى:",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(230, fieldsY),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            numMinStock = new NumericUpDown
            {
                Value = _product?.MinQuantity ?? 0,
                Font = new Font("Tahoma", 10F),
                Location = new Point(320, fieldsY - 2),
                Size = new Size(100, 25),
                Maximum = 999999,
                RightToLeft = RightToLeft.Yes
            };

            var maxStockLabel = new Label
            {
                Text = "الحد الأقصى:",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(440, fieldsY),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            numMaxStock = new NumericUpDown
            {
                Value = _product?.MaxQuantity ?? 0,
                Font = new Font("Tahoma", 10F),
                Location = new Point(530, fieldsY - 2),
                Size = new Size(100, 25),
                Maximum = 999999,
                RightToLeft = RightToLeft.Yes
            };

            fieldsY += 50;

            // أزرار الحفظ والإغلاق
            btnSave = new Button
            {
                Text = "💾 حفظ",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                Size = new Size(100, 35),
                Location = new Point(20, fieldsY),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            btnSave.FlatAppearance.BorderSize = 0;
            btnSave.Click += BtnSave_Click;

            btnClose = new Button
            {
                Text = "❌ إغلاق",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                Size = new Size(100, 35),
                Location = new Point(130, fieldsY),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            btnClose.FlatAppearance.BorderSize = 0;
            btnClose.Click += (s, e) => this.Close();

            // إضافة جميع العناصر للوحة
            parentPanel.Controls.AddRange(new Control[] {
                nameLabel, txtName, descriptionLabel, txtDescription,
                barcodeLabel, txtBarcode,
                supplierLabel, cmbSupplier, purchasePriceLabel, numPurchasePrice, salePriceLabel, numSalePrice,
                currentStockLabel, numCurrentStock, minStockLabel, numMinStock, maxStockLabel, numMaxStock,
                btnSave, btnClose
            });
        }

        // دوال مساعدة للحصول على المعلومات
        private string GetSupplierName()
        {
            if (_product?.SupplierId > 0)
            {
                var supplier = SimpleDataManager.Instance.GetSupplierById(_product.SupplierId);
                return supplier?.Name ?? "غير محدد";
            }
            return "غير محدد";
        }

        private string GetSupplierInfo()
        {
            if (_product?.SupplierId > 0)
            {
                var supplier = SimpleDataManager.Instance.GetSupplierById(_product.SupplierId);
                if (supplier != null)
                {
                    return $"{supplier.Company} - {supplier.Phone}";
                }
            }
            return "لا توجد معلومات";
        }

        private string GetStockStatusText()
        {
            if (_product.Quantity <= 0)
                return "نفد المخزون";
            else if (_product.Quantity <= _product.MinQuantity)
                return "أقل من الحد الأدنى";
            else
                return "متوفر";
        }

        private void LoadCategoriesToCombo(ComboBox combo)
        {
            try
            {
                var products = SimpleDataManager.Instance.GetAllProducts();
                var categories = products.Where(p => !string.IsNullOrEmpty(p.Category))
                                       .Select(p => p.Category)
                                       .Distinct()
                                       .OrderBy(c => c)
                                       .ToList();

                combo.Items.Clear();
                combo.Items.Add("-- اختر التصنيف --");
                combo.Items.AddRange(categories.ToArray());

                // تحديد التصنيف الحالي
                if (!string.IsNullOrEmpty(_product?.Category))
                {
                    var index = combo.Items.IndexOf(_product.Category);
                    if (index >= 0)
                    {
                        combo.SelectedIndex = index;
                    }
                }
                else
                {
                    combo.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل التصنيفات: {ex.Message}");
            }
        }

        private void LoadSuppliersToCombo(ComboBox combo)
        {
            try
            {
                var suppliers = SimpleDataManager.Instance.GetAllSuppliers();
                combo.Items.Clear();
                combo.Items.Add("-- اختر المورد --");

                foreach (var supplier in suppliers.Where(s => s.IsActive))
                {
                    combo.Items.Add($"{supplier.Name} - {supplier.Company}");
                }

                // تحديد المورد الحالي
                if (_product?.SupplierId > 0)
                {
                    var currentSupplier = suppliers.FirstOrDefault(s => s.Id == _product.SupplierId);
                    if (currentSupplier != null)
                    {
                        var itemText = $"{currentSupplier.Name} - {currentSupplier.Company}";
                        var index = combo.Items.IndexOf(itemText);
                        if (index >= 0)
                        {
                            combo.SelectedIndex = index;
                        }
                    }
                }
                else
                {
                    combo.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الموردين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadSuppliersToCombo()
        {
            try
            {
                var suppliers = SimpleDataManager.Instance.GetAllSuppliers();
                cmbSupplier.Items.Clear();
                cmbSupplier.Items.Add("-- اختر المورد --");

                foreach (var supplier in suppliers.Where(s => s.IsActive))
                {
                    cmbSupplier.Items.Add($"{supplier.Name} - {supplier.Company}");
                }

                // تحديد المورد الحالي
                if (_product?.SupplierId > 0)
                {
                    var currentSupplier = suppliers.FirstOrDefault(s => s.Id == _product.SupplierId);
                    if (currentSupplier != null)
                    {
                        var itemText = $"{currentSupplier.Name} - {currentSupplier.Company}";
                        var index = cmbSupplier.Items.IndexOf(itemText);
                        if (index >= 0)
                        {
                            cmbSupplier.SelectedIndex = index;
                        }
                    }
                }
                else
                {
                    cmbSupplier.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الموردين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private decimal CalculateTotalSales()
        {
            try
            {
                var invoices = SimpleDataManager.Instance.GetAllInvoices();
                decimal totalSales = 0;

                foreach (var invoice in invoices)
                {
                    var productItems = invoice.Items.Where(item => item.ProductId == _product.Id);
                    totalSales += productItems.Sum(item => item.Total);
                }

                return totalSales;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب إجمالي المبيعات: {ex.Message}");
                return 0;
            }
        }

        private void LoadProductStockMovements()
        {
            try
            {
                var movements = SimpleDataManager.Instance.GetStockMovementsByProduct(_product.Id);
                stockMovementsGrid.Rows.Clear();

                foreach (var movement in movements)
                {
                    var row = stockMovementsGrid.Rows[stockMovementsGrid.Rows.Add()];
                    row.Cells["MovementType"].Value = movement.MovementType;
                    row.Cells["Quantity"].Value = movement.Quantity;
                    row.Cells["UnitPrice"].Value = $"{movement.UnitPrice:N2}";
                    row.Cells["TotalValue"].Value = $"{movement.TotalValue:N2}";
                    row.Cells["Reference"].Value = movement.Reference;
                    row.Cells["Date"].Value = movement.MovementDate.ToString("dd/MM/yyyy HH:mm");
                    row.Cells["Notes"].Value = movement.Notes;

                    // تلوين الصفوف حسب نوع الحركة
                    if (movement.MovementType == "دخول")
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(240, 255, 240);
                        row.DefaultCellStyle.ForeColor = Color.FromArgb(0, 128, 0);
                    }
                    else if (movement.MovementType == "خروج")
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(255, 240, 240);
                        row.DefaultCellStyle.ForeColor = Color.FromArgb(128, 0, 0);
                    }
                    else if (movement.MovementType == "تسوية")
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(240, 240, 255);
                        row.DefaultCellStyle.ForeColor = Color.FromArgb(0, 0, 128);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل حركات المخزون: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // أحداث الأزرار الجديدة
        private void BtnAddCategory_Click(object sender, EventArgs e)
        {
            var categoryForm = new Form
            {
                Text = "إضافة تصنيف جديد",
                Size = new Size(400, 200),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false,
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true
            };

            var label = new Label
            {
                Text = "اسم التصنيف:",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                Location = new Point(20, 30),
                Size = new Size(100, 25)
            };

            var textBox = new TextBox
            {
                Font = new Font("Tahoma", 10F),
                Location = new Point(130, 30),
                Size = new Size(200, 25),
                RightToLeft = RightToLeft.Yes
            };

            var btnOK = new Button
            {
                Text = "إضافة",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                Location = new Point(130, 80),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnOK.FlatAppearance.BorderSize = 0;

            var btnCancel = new Button
            {
                Text = "إلغاء",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                Location = new Point(220, 80),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnCancel.FlatAppearance.BorderSize = 0;

            btnOK.Click += (s, args) => {
                if (!string.IsNullOrWhiteSpace(textBox.Text))
                {
                    cmbCategory.Items.Add(textBox.Text.Trim());
                    cmbCategory.SelectedItem = textBox.Text.Trim();
                    categoryForm.DialogResult = DialogResult.OK;
                    categoryForm.Close();
                }
                else
                {
                    MessageBox.Show("يرجى إدخال اسم التصنيف", "تحذير",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            };

            btnCancel.Click += (s, args) => {
                categoryForm.DialogResult = DialogResult.Cancel;
                categoryForm.Close();
            };

            categoryForm.Controls.AddRange(new Control[] { label, textBox, btnOK, btnCancel });
            categoryForm.ShowDialog();
        }

        private void BtnPrintReport_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("ميزة طباعة التقرير قيد التطوير", "معلومات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnExportData_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("ميزة تصدير البيانات قيد التطوير", "معلومات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المنتج", "تحذير",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return;
                }

                // تحديث بيانات المنتج
                _product.Name = txtName.Text.Trim();
                _product.Description = txtDescription.Text.Trim();
                _product.Barcode = txtBarcode.Text.Trim();
                _product.Unit = txtUnit.Text.Trim();
                _product.PurchasePrice = numPurchasePrice.Value;
                _product.Price = numSalePrice.Value;
                _product.Quantity = (int)numCurrentStock.Value;
                _product.MinQuantity = (int)numMinStock.Value;
                _product.MaxQuantity = (int)numMaxStock.Value;

                // تحديد التصنيف
                if (cmbCategory.SelectedIndex > 0)
                {
                    _product.Category = cmbCategory.SelectedItem.ToString();
                }
                else if (cmbCategory.SelectedIndex == 0)
                {
                    _product.Category = "";
                }

                // تحديد المورد
                if (cmbSupplier.SelectedIndex > 0)
                {
                    var suppliers = SimpleDataManager.Instance.GetAllSuppliers();
                    var selectedText = cmbSupplier.SelectedItem.ToString();
                    var supplier = suppliers.FirstOrDefault(s =>
                        selectedText.Contains(s.Name) && selectedText.Contains(s.Company));
                    if (supplier != null)
                    {
                        _product.SupplierId = supplier.Id;
                    }
                }
                else
                {
                    _product.SupplierId = 0;
                }

                // حفظ التغييرات
                SimpleDataManager.Instance.UpdateProduct(_product);

                // تحديث العرض
                lblProductName.Text = _product.Name;
                this.Text = $"تفاصيل المنتج - {_product.Name}";

                MessageBox.Show("تم حفظ التغييرات بنجاح!", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التغييرات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadSalesHistory()
        {
            try
            {
                var invoices = SimpleDataManager.Instance.GetAllInvoices();
                salesHistoryGrid.Rows.Clear();

                foreach (var invoice in invoices)
                {
                    var productItems = invoice.Items.Where(item => item.ProductId == _product.Id);
                    foreach (var item in productItems)
                    {
                        var customer = SimpleDataManager.Instance.GetCustomerById(invoice.CustomerId);
                        var row = salesHistoryGrid.Rows[salesHistoryGrid.Rows.Add()];

                        row.Cells["InvoiceNumber"].Value = invoice.InvoiceNumber;
                        row.Cells["CustomerName"].Value = customer?.Name ?? "غير محدد";
                        row.Cells["Quantity"].Value = $"{item.Quantity} {_product.Unit}";
                        row.Cells["UnitPrice"].Value = $"{item.Price:N2} ريال";
                        row.Cells["TotalAmount"].Value = $"{item.Total:N2} ريال";
                        row.Cells["SaleDate"].Value = invoice.Date.ToString("dd/MM/yyyy HH:mm");

                        // تلوين الصفوف بالتناوب
                        if (salesHistoryGrid.Rows.Count % 2 == 0)
                        {
                            row.DefaultCellStyle.BackColor = Color.FromArgb(248, 250, 252);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تاريخ المبيعات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
