using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;

namespace SimpleAccounting
{
    public partial class ProductDetailsForm : Form
    {
        private SimpleProduct _product;
        private Panel mainPanel;
        private Panel productInfoPanel;
        private Panel stockMovementsPanel;
        private DataGridView stockMovementsGrid;
        private Label lblProductName;
        private Label lblCurrentStock;
        private Label lblPurchasePrice;
        private Label lblSalePrice;
        private Label lblTotalSales;
        private Label lblLastMovement;
        private TextBox txtName;
        private TextBox txtDescription;
        private TextBox txtBarcode;
        private TextBox txtCategory;
        private ComboBox cmbSupplier;
        private NumericUpDown numPurchasePrice;
        private NumericUpDown numSalePrice;
        private NumericUpDown numCurrentStock;
        private NumericUpDown numMinStock;
        private NumericUpDown numMaxStock;
        private Button btnSave;
        private Button btnClose;

        public ProductDetailsForm(int productId)
        {
            LoadProductData(productId);
            InitializeComponent();
            SetupModernUI();
            LoadProductStockMovements();
        }

        private void LoadProductData(int productId)
        {
            try
            {
                _product = SimpleDataManager.Instance.GetProductById(productId);
                if (_product == null)
                {
                    throw new Exception("المنتج غير موجود");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.DialogResult = DialogResult.Cancel;
            }
        }

        private void InitializeComponent()
        {
            this.Text = $"تفاصيل المنتج - {_product?.Name ?? "غير محدد"}";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(240, 244, 248);

            // إنشاء اللوحة الرئيسية
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(20)
            };

            // إنشاء لوحة معلومات المنتج
            productInfoPanel = CreateProductInfoPanel();
            productInfoPanel.Dock = DockStyle.Top;
            productInfoPanel.Height = 450;

            // إنشاء لوحة حركات المخزون
            stockMovementsPanel = CreateStockMovementsPanel();
            stockMovementsPanel.Dock = DockStyle.Fill;

            mainPanel.Controls.AddRange(new Control[] {
                productInfoPanel, stockMovementsPanel
            });

            this.Controls.Add(mainPanel);
        }

        private void SetupModernUI()
        {
            // تطبيق التصميم الحديث
            this.Font = new Font("Tahoma", 9F);
            
            // إضافة تأثيرات بصرية للنافذة
            this.Paint += (s, e) => {
                var graphics = e.Graphics;
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                
                // رسم حد ملون للنافذة
                using (var pen = new Pen(Color.FromArgb(52, 152, 219), 2))
                {
                    graphics.DrawRectangle(pen, 1, 1, this.Width - 3, this.Height - 3);
                }
            };
        }

        private Panel CreateProductInfoPanel()
        {
            var panel = new Panel
            {
                BackColor = Color.White,
                Padding = new Padding(20),
                Margin = new Padding(0, 0, 0, 10)
            };

            // إضافة تأثيرات ثلاثية الأبعاد
            panel.Paint += (s, e) => {
                var graphics = e.Graphics;
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                // رسم ظل
                using (var shadowBrush = new SolidBrush(Color.FromArgb(20, 0, 0, 0)))
                {
                    graphics.FillRectangle(shadowBrush, 3, 3, panel.Width - 3, panel.Height - 3);
                }

                // رسم خلفية متدرجة
                using (var brush = new LinearGradientBrush(
                    panel.ClientRectangle,
                    Color.White,
                    Color.FromArgb(248, 250, 252),
                    LinearGradientMode.Vertical))
                {
                    graphics.FillRectangle(brush, panel.ClientRectangle);
                }

                // رسم حد
                using (var pen = new Pen(Color.FromArgb(220, 223, 230), 1))
                {
                    graphics.DrawRectangle(pen, 0, 0, panel.Width - 1, panel.Height - 1);
                }
            };

            // العنوان
            var titleLabel = new Label
            {
                Text = "📦 معلومات المنتج",
                Font = new Font("Tahoma", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 20),
                Size = new Size(300, 30),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // اسم المنتج الكبير
            lblProductName = new Label
            {
                Text = _product?.Name ?? "",
                Font = new Font("Tahoma", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(46, 204, 113),
                Location = new Point(20, 60),
                Size = new Size(400, 35),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // إنشاء بطاقات الملخص
            CreateProductSummaryCards(panel);

            // حقول التعديل في الجزء السفلي
            CreateEditingFields(panel);

            panel.Controls.AddRange(new Control[] {
                titleLabel, lblProductName
            });

            return panel;
        }

        private Panel CreateStockMovementsPanel()
        {
            var panel = new Panel
            {
                BackColor = Color.White,
                Padding = new Padding(20),
                Margin = new Padding(0, 10, 0, 0)
            };

            // إضافة تأثيرات ثلاثية الأبعاد
            panel.Paint += (s, e) => {
                var graphics = e.Graphics;
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                // رسم ظل
                using (var shadowBrush = new SolidBrush(Color.FromArgb(20, 0, 0, 0)))
                {
                    graphics.FillRectangle(shadowBrush, 3, 3, panel.Width - 3, panel.Height - 3);
                }

                // رسم خلفية متدرجة
                using (var brush = new LinearGradientBrush(
                    panel.ClientRectangle,
                    Color.White,
                    Color.FromArgb(248, 250, 252),
                    LinearGradientMode.Vertical))
                {
                    graphics.FillRectangle(brush, panel.ClientRectangle);
                }

                // رسم حد
                using (var pen = new Pen(Color.FromArgb(220, 223, 230), 1))
                {
                    graphics.DrawRectangle(pen, 0, 0, panel.Width - 1, panel.Height - 1);
                }
            };

            // العنوان
            var titleLabel = new Label
            {
                Text = "📊 حركات المخزون",
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 20),
                Size = new Size(300, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // إنشاء جدول حركات المخزون
            stockMovementsGrid = new DataGridView
            {
                Location = new Point(20, 60),
                Size = new Size(panel.Width - 40, panel.Height - 100),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                GridColor = Color.FromArgb(230, 230, 230),
                RowHeadersVisible = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                Font = new Font("Tahoma", 9F),
                RightToLeft = RightToLeft.Yes
            };

            // تنسيق رأس الجدول
            stockMovementsGrid.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 152, 219);
            stockMovementsGrid.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            stockMovementsGrid.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            stockMovementsGrid.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            stockMovementsGrid.ColumnHeadersHeight = 40;

            // إضافة الأعمدة
            stockMovementsGrid.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "MovementType", HeaderText = "نوع الحركة", FillWeight = 15 },
                new DataGridViewTextBoxColumn { Name = "Quantity", HeaderText = "الكمية", FillWeight = 15 },
                new DataGridViewTextBoxColumn { Name = "UnitPrice", HeaderText = "سعر الوحدة", FillWeight = 15 },
                new DataGridViewTextBoxColumn { Name = "TotalValue", HeaderText = "القيمة الإجمالية", FillWeight = 20 },
                new DataGridViewTextBoxColumn { Name = "Reference", HeaderText = "المرجع", FillWeight = 15 },
                new DataGridViewTextBoxColumn { Name = "Date", HeaderText = "التاريخ", FillWeight = 15 },
                new DataGridViewTextBoxColumn { Name = "Notes", HeaderText = "ملاحظات", FillWeight = 25 }
            });

            panel.Controls.AddRange(new Control[] {
                titleLabel, stockMovementsGrid
            });

            return panel;
        }

        private void CreateProductSummaryCards(Panel parentPanel)
        {
            try
            {
                // حساب القيم المالية
                var stockMovements = SimpleDataManager.Instance.GetStockMovementsByProduct(_product.Id);
                var totalSales = CalculateTotalSales();
                var lastMovement = stockMovements.FirstOrDefault();

                // إنشاء لوحة البطاقات مع حجم أكبر
                var cardsPanel = new Panel
                {
                    Location = new Point(20, 110),
                    Size = new Size(Math.Max(1000, parentPanel.Width - 40), 120),
                    BackColor = Color.Transparent,
                    AutoScroll = false
                };

                // حساب عرض البطاقة بناءً على المساحة المتاحة
                int cardWidth = Math.Max(180, (cardsPanel.Width - 40) / 5); // 5 بطاقات
                int cardSpacing = 10;

                // بطاقة الكمية المتاحة
                var stockCard = CreateInfoCard("الكمية المتاحة", $"{_product.Quantity} {_product.Unit}", "📦",
                    Color.FromArgb(46, 204, 113), new Point(0, 0), new Size(cardWidth, 100));

                // بطاقة سعر الشراء
                var purchasePriceCard = CreateInfoCard("سعر الشراء", $"{_product.PurchasePrice:N2} ريال", "💰",
                    Color.FromArgb(155, 89, 182), new Point(cardWidth + cardSpacing, 0), new Size(cardWidth, 100));

                // بطاقة سعر البيع
                var salePriceCard = CreateInfoCard("سعر البيع", $"{_product.Price:N2} ريال", "🏷️",
                    Color.FromArgb(52, 152, 219), new Point((cardWidth + cardSpacing) * 2, 0), new Size(cardWidth, 100));

                // بطاقة إجمالي المبيعات
                var totalSalesCard = CreateInfoCard("إجمالي المبيعات", $"{totalSales:N2} ريال", "📈",
                    Color.FromArgb(230, 126, 34), new Point((cardWidth + cardSpacing) * 3, 0), new Size(cardWidth, 100));

                // بطاقة آخر حركة مخزون
                var lastMovementText = lastMovement?.MovementDate.ToString("dd/MM/yyyy") ?? "لا توجد";
                var lastMovementCard = CreateInfoCard("آخر حركة مخزون", lastMovementText, "📊",
                    Color.FromArgb(231, 76, 60), new Point((cardWidth + cardSpacing) * 4, 0), new Size(cardWidth, 100));

                cardsPanel.Controls.AddRange(new Control[] {
                    stockCard, purchasePriceCard, salePriceCard, totalSalesCard, lastMovementCard
                });

                parentPanel.Controls.Add(cardsPanel);

                // حفظ المراجع للتحديث لاحقاً
                lblCurrentStock = stockCard.Controls.OfType<Label>().LastOrDefault();
                lblPurchasePrice = purchasePriceCard.Controls.OfType<Label>().LastOrDefault();
                lblSalePrice = salePriceCard.Controls.OfType<Label>().LastOrDefault();
                lblTotalSales = totalSalesCard.Controls.OfType<Label>().LastOrDefault();
                lblLastMovement = lastMovementCard.Controls.OfType<Label>().LastOrDefault();

                System.Diagnostics.Debug.WriteLine($"تم إنشاء {cardsPanel.Controls.Count} بطاقات للمنتج");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء البطاقات: {ex.Message}");
                MessageBox.Show($"خطأ في إنشاء البطاقات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private Panel CreateInfoCard(string title, string value, string icon, Color color, Point location, Size? customSize = null)
        {
            var cardSize = customSize ?? new Size(180, 80);
            var card = new Panel
            {
                Size = cardSize,
                Location = location,
                BackColor = Color.White,
                BorderStyle = BorderStyle.None
            };

            // إضافة تأثيرات ثلاثية الأبعاد
            card.Paint += (s, e) => {
                var graphics = e.Graphics;
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                // رسم ظل
                using (var shadowBrush = new SolidBrush(Color.FromArgb(30, 0, 0, 0)))
                {
                    graphics.FillRectangle(shadowBrush, 2, 2, card.Width - 2, card.Height - 2);
                }

                // رسم خلفية متدرجة
                using (var brush = new LinearGradientBrush(
                    card.ClientRectangle,
                    Color.White,
                    Color.FromArgb(250, 252, 255),
                    LinearGradientMode.Vertical))
                {
                    graphics.FillRectangle(brush, card.ClientRectangle);
                }

                // رسم حد ملون
                using (var pen = new Pen(color, 3))
                {
                    graphics.DrawLine(pen, 0, 0, card.Width, 0);
                }

                // رسم حد خارجي
                using (var pen = new Pen(Color.FromArgb(220, 223, 230), 1))
                {
                    graphics.DrawRectangle(pen, 0, 0, card.Width - 1, card.Height - 1);
                }
            };

            var iconLabel = new Label
            {
                Text = icon,
                Font = new Font("Segoe UI Emoji", 18F),
                ForeColor = color,
                Size = new Size(50, 50),
                Location = new Point(10, 15),
                TextAlign = ContentAlignment.MiddleCenter
            };

            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Tahoma", 9F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Size = new Size(cardSize.Width - 70, 25),
                Location = new Point(65, 15),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var valueLabel = new Label
            {
                Text = value,
                Font = new Font("Tahoma", 11F, FontStyle.Bold),
                ForeColor = color,
                Size = new Size(cardSize.Width - 70, 30),
                Location = new Point(65, 45),
                TextAlign = ContentAlignment.MiddleLeft
            };

            card.Controls.AddRange(new Control[] {
                iconLabel, titleLabel, valueLabel
            });

            return card;
        }

        private void CreateEditingFields(Panel parentPanel)
        {
            // حقول التعديل - تحت البطاقات
            var fieldsY = 250;

            // الصف الأول: الاسم والوصف
            var nameLabel = new Label
            {
                Text = "الاسم:",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, fieldsY),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            txtName = new TextBox
            {
                Text = _product?.Name ?? "",
                Font = new Font("Tahoma", 10F),
                Location = new Point(110, fieldsY - 2),
                Size = new Size(200, 25),
                RightToLeft = RightToLeft.Yes
            };

            var descriptionLabel = new Label
            {
                Text = "الوصف:",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(330, fieldsY),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            txtDescription = new TextBox
            {
                Text = _product?.Description ?? "",
                Font = new Font("Tahoma", 10F),
                Location = new Point(420, fieldsY - 2),
                Size = new Size(200, 25),
                RightToLeft = RightToLeft.Yes
            };

            fieldsY += 40;

            // الصف الثاني: الباركود والفئة
            var barcodeLabel = new Label
            {
                Text = "الباركود:",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, fieldsY),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            txtBarcode = new TextBox
            {
                Text = _product?.Barcode ?? "",
                Font = new Font("Tahoma", 10F),
                Location = new Point(110, fieldsY - 2),
                Size = new Size(200, 25),
                RightToLeft = RightToLeft.Yes
            };

            var categoryLabel = new Label
            {
                Text = "الفئة:",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(330, fieldsY),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            txtCategory = new TextBox
            {
                Text = _product?.Category ?? "",
                Font = new Font("Tahoma", 10F),
                Location = new Point(420, fieldsY - 2),
                Size = new Size(200, 25),
                RightToLeft = RightToLeft.Yes
            };

            fieldsY += 40;

            // الصف الثالث: المورد وأسعار
            var supplierLabel = new Label
            {
                Text = "المورد:",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, fieldsY),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            cmbSupplier = new ComboBox
            {
                Font = new Font("Tahoma", 10F),
                Location = new Point(110, fieldsY - 2),
                Size = new Size(200, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes
            };
            LoadSuppliersToCombo();

            var purchasePriceLabel = new Label
            {
                Text = "سعر الشراء:",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(330, fieldsY),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            numPurchasePrice = new NumericUpDown
            {
                Value = _product?.PurchasePrice ?? 0,
                Font = new Font("Tahoma", 10F),
                Location = new Point(420, fieldsY - 2),
                Size = new Size(100, 25),
                DecimalPlaces = 2,
                Maximum = 999999,
                RightToLeft = RightToLeft.Yes
            };

            var salePriceLabel = new Label
            {
                Text = "سعر البيع:",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(540, fieldsY),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            numSalePrice = new NumericUpDown
            {
                Value = _product?.Price ?? 0,
                Font = new Font("Tahoma", 10F),
                Location = new Point(630, fieldsY - 2),
                Size = new Size(100, 25),
                DecimalPlaces = 2,
                Maximum = 999999,
                RightToLeft = RightToLeft.Yes
            };

            fieldsY += 40;

            // الصف الرابع: كميات المخزون
            var currentStockLabel = new Label
            {
                Text = "الكمية الحالية:",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, fieldsY),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            numCurrentStock = new NumericUpDown
            {
                Value = _product?.Quantity ?? 0,
                Font = new Font("Tahoma", 10F),
                Location = new Point(110, fieldsY - 2),
                Size = new Size(100, 25),
                Maximum = 999999,
                RightToLeft = RightToLeft.Yes
            };

            var minStockLabel = new Label
            {
                Text = "الحد الأدنى:",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(230, fieldsY),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            numMinStock = new NumericUpDown
            {
                Value = _product?.MinQuantity ?? 0,
                Font = new Font("Tahoma", 10F),
                Location = new Point(320, fieldsY - 2),
                Size = new Size(100, 25),
                Maximum = 999999,
                RightToLeft = RightToLeft.Yes
            };

            var maxStockLabel = new Label
            {
                Text = "الحد الأقصى:",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(440, fieldsY),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            numMaxStock = new NumericUpDown
            {
                Value = _product?.MaxQuantity ?? 0,
                Font = new Font("Tahoma", 10F),
                Location = new Point(530, fieldsY - 2),
                Size = new Size(100, 25),
                Maximum = 999999,
                RightToLeft = RightToLeft.Yes
            };

            fieldsY += 50;

            // أزرار الحفظ والإغلاق
            btnSave = new Button
            {
                Text = "💾 حفظ",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                Size = new Size(100, 35),
                Location = new Point(20, fieldsY),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            btnSave.FlatAppearance.BorderSize = 0;
            btnSave.Click += BtnSave_Click;

            btnClose = new Button
            {
                Text = "❌ إغلاق",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                Size = new Size(100, 35),
                Location = new Point(130, fieldsY),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            btnClose.FlatAppearance.BorderSize = 0;
            btnClose.Click += (s, e) => this.Close();

            // إضافة جميع العناصر للوحة
            parentPanel.Controls.AddRange(new Control[] {
                nameLabel, txtName, descriptionLabel, txtDescription,
                barcodeLabel, txtBarcode, categoryLabel, txtCategory,
                supplierLabel, cmbSupplier, purchasePriceLabel, numPurchasePrice, salePriceLabel, numSalePrice,
                currentStockLabel, numCurrentStock, minStockLabel, numMinStock, maxStockLabel, numMaxStock,
                btnSave, btnClose
            });
        }

        private void LoadSuppliersToCombo()
        {
            try
            {
                var suppliers = SimpleDataManager.Instance.GetAllSuppliers();
                cmbSupplier.Items.Clear();
                cmbSupplier.Items.Add("-- اختر المورد --");

                foreach (var supplier in suppliers.Where(s => s.IsActive))
                {
                    cmbSupplier.Items.Add($"{supplier.Name} - {supplier.Company}");
                }

                // تحديد المورد الحالي
                if (_product?.SupplierId > 0)
                {
                    var currentSupplier = suppliers.FirstOrDefault(s => s.Id == _product.SupplierId);
                    if (currentSupplier != null)
                    {
                        var itemText = $"{currentSupplier.Name} - {currentSupplier.Company}";
                        var index = cmbSupplier.Items.IndexOf(itemText);
                        if (index >= 0)
                        {
                            cmbSupplier.SelectedIndex = index;
                        }
                    }
                }
                else
                {
                    cmbSupplier.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الموردين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private decimal CalculateTotalSales()
        {
            try
            {
                var invoices = SimpleDataManager.Instance.GetAllInvoices();
                decimal totalSales = 0;

                foreach (var invoice in invoices)
                {
                    var productItems = invoice.Items.Where(item => item.ProductId == _product.Id);
                    totalSales += productItems.Sum(item => item.Total);
                }

                return totalSales;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب إجمالي المبيعات: {ex.Message}");
                return 0;
            }
        }

        private void LoadProductStockMovements()
        {
            try
            {
                var movements = SimpleDataManager.Instance.GetStockMovementsByProduct(_product.Id);
                stockMovementsGrid.Rows.Clear();

                foreach (var movement in movements)
                {
                    var row = stockMovementsGrid.Rows[stockMovementsGrid.Rows.Add()];
                    row.Cells["MovementType"].Value = movement.MovementType;
                    row.Cells["Quantity"].Value = movement.Quantity;
                    row.Cells["UnitPrice"].Value = $"{movement.UnitPrice:N2}";
                    row.Cells["TotalValue"].Value = $"{movement.TotalValue:N2}";
                    row.Cells["Reference"].Value = movement.Reference;
                    row.Cells["Date"].Value = movement.MovementDate.ToString("dd/MM/yyyy HH:mm");
                    row.Cells["Notes"].Value = movement.Notes;

                    // تلوين الصفوف حسب نوع الحركة
                    if (movement.MovementType == "دخول")
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(240, 255, 240);
                        row.DefaultCellStyle.ForeColor = Color.FromArgb(0, 128, 0);
                    }
                    else if (movement.MovementType == "خروج")
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(255, 240, 240);
                        row.DefaultCellStyle.ForeColor = Color.FromArgb(128, 0, 0);
                    }
                    else if (movement.MovementType == "تسوية")
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(240, 240, 255);
                        row.DefaultCellStyle.ForeColor = Color.FromArgb(0, 0, 128);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل حركات المخزون: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المنتج", "تحذير",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return;
                }

                // تحديث بيانات المنتج
                _product.Name = txtName.Text.Trim();
                _product.Description = txtDescription.Text.Trim();
                _product.Barcode = txtBarcode.Text.Trim();
                _product.Category = txtCategory.Text.Trim();
                _product.PurchasePrice = numPurchasePrice.Value;
                _product.Price = numSalePrice.Value;
                _product.Quantity = (int)numCurrentStock.Value;
                _product.MinQuantity = (int)numMinStock.Value;
                _product.MaxQuantity = (int)numMaxStock.Value;

                // تحديد المورد
                if (cmbSupplier.SelectedIndex > 0)
                {
                    var suppliers = SimpleDataManager.Instance.GetAllSuppliers();
                    var selectedText = cmbSupplier.SelectedItem.ToString();
                    var supplier = suppliers.FirstOrDefault(s =>
                        selectedText.Contains(s.Name) && selectedText.Contains(s.Company));
                    if (supplier != null)
                    {
                        _product.SupplierId = supplier.Id;
                    }
                }

                // حفظ التغييرات
                SimpleDataManager.Instance.UpdateProduct(_product);

                // تحديث العرض
                lblProductName.Text = _product.Name;
                this.Text = $"تفاصيل المنتج - {_product.Name}";

                MessageBox.Show("تم حفظ التغييرات بنجاح!", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التغييرات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
