using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using System.Linq;
using System.Collections.Generic;

namespace SimpleAccounting
{
    public partial class MainForm : Form
    {
        private Panel sidePanel;
        private Panel contentPanel;
        // تم حذف topPanel وجميع عناصره (lblWelcome, lblDateTime, timeTimer)

        // أزرار القائمة الجانبية
        private Panel btnDashboard;
        private Panel btnSales;
        // تم حذف btnPurchases - وحدة المشتريات محذوفة
        private Panel btnInventory;
        private Panel btnCustomers;
        private Panel btnSuppliers;
        private Panel btnReports;
        private Panel btnSettings;
        private Panel btnLogout;
        private Panel userProfilePanel;

        public MainForm()
        {
            InitializeComponent();
            SetupModernUI();
            // تم حذف SetupTimer - لا حاجة له بعد إزالة الشريط العلوي
            LoadDashboard();

            // إضافة حدث لإعادة تخطيط العناصر عند تغيير حجم النافذة
            this.Resize += MainForm_Resize;
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعدادات النافذة الأساسية المحسنة
            this.Text = "نظام المحاسبة الشامل - التصميم الثلاثي الأبعاد المحسن";
            this.Size = new Size(1600, 900); // تكبير حجم النافذة
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.BackColor = Color.FromArgb(245, 248, 252); // لون خلفية أكثر نعومة
            this.Font = new Font("Segoe UI", 11F, FontStyle.Regular); // خط أكثر عصرية

            // اللوحة الجانبية المحسنة
            sidePanel = new Panel
            {
                Dock = DockStyle.Left,
                Width = 90, // تكبير العرض
                BackColor = Color.FromArgb(44, 62, 80) // لون داكن محسن
            };

            // لوحة المحتوى المحسنة
            contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(248, 251, 255), // خلفية أكثر نعومة
                Padding = new Padding(25) // تباعد أكبر
            };

            // تعديل موضع اللوحة الجانبية
            sidePanel.Location = new Point(0, 0);
            sidePanel.Height = this.ClientSize.Height;

            // إنشاء عناصر واجهة المستخدم الأخرى
            CreateTopControls();
            CreateMenuButtons();

            this.Controls.AddRange(new Control[] { contentPanel, sidePanel });
            this.ResumeLayout(false);
        }

        private void CreateMenuButtons()
        {
            //  زر تسجيل الخروج في الأعلى
            btnLogout = CreateModernMenuButton("🚪", Color.FromArgb(231, 76, 60), 20, () => LogoutUser(), "تسجيل الخروج");
            btnLogout.Location = new Point(12, 10); // أعلى القائمة مباشرة

            int buttonSize = 65; // تكبير حجم الأزرار
            int buttonSpacing = 20; // تكبير التباعد
            int currentY = 100; // موضع البداية المحسن

             // صورة المستخدم في الأعلى
            userProfilePanel = CreateUserProfile();
            userProfilePanel.Location = new Point(5, 45); // ضعها فوق القائمة
            currentY = 120; //  بعد الصورة الشخصية

            // باقي أزرار القائمة الرئيسية
            btnDashboard = CreateModernMenuButton("🏠", Color.FromArgb(52, 152, 219), currentY, () => LoadDashboard(), "لوحة التحكم");
            currentY += buttonSize + buttonSpacing;

            btnSales = CreateModernMenuButton("💰", Color.FromArgb(46, 204, 113), currentY, () => LoadSalesModule(), "المبيعات");
            currentY += buttonSize + buttonSpacing;

            btnInventory = CreateModernMenuButton("📦", Color.FromArgb(230, 126, 34), currentY, () => LoadInventoryModule(), "المخزون");
            currentY += buttonSize + buttonSpacing;

            btnCustomers = CreateModernMenuButton("👥", Color.FromArgb(155, 89, 182), currentY, () => LoadCustomersModule(), "العملاء");
            currentY += buttonSize + buttonSpacing;

            btnSuppliers = CreateModernMenuButton("🏭", Color.FromArgb(52, 73, 94), currentY, () => LoadSuppliersModule(), "الموردين");
            currentY += buttonSize + buttonSpacing;
        }

        private void CreateTopControls()
        {
            int buttonSize = 65; // تكبير حجم الأزرار
            int buttonSpacing = 20; // تكبير التباعد
            int currentY = 25; // موضع البداية المحسن

            // إنشاء صورة المستخدم في الأعلى المحسنة
            userProfilePanel = CreateUserProfile();
            currentY = 120; // موضع محسن بعد الصورة الشخصية

            // زر لوحة التحكم - محسن
            btnDashboard = CreateModernMenuButton("🏠", Color.FromArgb(52, 152, 219), currentY, () => LoadDashboard(), "لوحة التحكم");
            currentY += buttonSize + buttonSpacing;

            // زر المبيعات - محسن
            btnSales = CreateModernMenuButton("💰", Color.FromArgb(46, 204, 113), currentY, () => LoadSalesModule(), "المبيعات");
            currentY += buttonSize + buttonSpacing;


            // زر العملاء - محسن
            btnCustomers = CreateModernMenuButton("👥", Color.FromArgb(155, 89, 182), currentY, () => LoadCustomersModule(), "العملاء");
            currentY += buttonSize + buttonSpacing;

            // زر الموردين - محسن
            btnSuppliers = CreateModernMenuButton("🏭", Color.FromArgb(52, 73, 94), currentY, () => LoadSuppliersModule(), "الموردين");
            currentY += buttonSize + buttonSpacing;

            // زر التقارير - محسن
            btnReports = CreateModernMenuButton("📊", Color.FromArgb(231, 76, 60), currentY, () => LoadReportsModule(), "التقارير");
            currentY += buttonSize + buttonSpacing;

            // زر الإعدادات - محسن
            btnSettings = CreateModernMenuButton("⚙️", Color.FromArgb(149, 165, 166), currentY, () => LoadSettingsModule(), "الإعدادات");
            currentY += buttonSize + buttonSpacing;

            // زر تسجيل الخروج في الأسفل - محسن
            btnLogout = CreateModernMenuButton("🚪", Color.FromArgb(231, 76, 60), 750, () => LogoutUser(), "تسجيل الخروج");

            // إضافة الأزرار للوحة الجانبية (بدون زر المشتريات)
            sidePanel.Controls.AddRange(new Control[] {
                userProfilePanel, btnDashboard, btnSales, btnInventory, btnCustomers,
                btnSuppliers, btnReports, btnSettings, btnLogout
            });

            // تأجيل تحديد موضع زر الخروج إلى ما بعد رسم النافذة
            this.Load += MainForm_Load;
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            // تحديد موضع زر الخروج بعد رسم النافذة - محسن
            if (btnLogout != null && sidePanel.Height > 120)
            {
                btnLogout.Location = new Point(12, sidePanel.Height - 90); // موضع محسن
            }
        }

        private Panel CreateUserProfile()
        {
            var profilePanel = new Panel
            {
                Size = new Size(80, 80), // تكبير الحجم
                Location = new Point(5, 20), // موضع محسن
                BackColor = Color.Transparent
            };

            // إنشاء دائرة للصورة الشخصية المحسنة
            var profilePicture = new Panel
            {
                Size = new Size(70, 70), // تكبير الحجم
                Location = new Point(5, 5),
                BackColor = Color.FromArgb(52, 152, 219),
                Cursor = Cursors.Hand
            };

            // إضافة أيقونة المستخدم المحسنة
            var userIcon = new Label
            {
                Text = "👤",
                Font = new Font("Segoe UI Emoji", 28F), // تكبير الخط
                ForeColor = Color.White,
                Size = new Size(70, 70), // تكبير الحجم
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            profilePicture.Controls.Add(userIcon);
            profilePanel.Controls.Add(profilePicture);

            // جعل الصورة دائرية مع تأثيرات ثلاثية الأبعاد
            profilePicture.Paint += (s, e) => {
                var graphics = e.Graphics;
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                // رسم ظل ثلاثي الأبعاد
                using (var shadowBrush = new SolidBrush(Color.FromArgb(80, 0, 0, 0)))
                {
                    graphics.FillEllipse(shadowBrush, 3, 3, profilePicture.Width - 3, profilePicture.Height - 3);
                }

                // رسم تدرج ثلاثي الأبعاد
                using (var brush = new LinearGradientBrush(
                    new Rectangle(0, 0, profilePicture.Width, profilePicture.Height),
                    Color.FromArgb(74, 172, 239), // لون فاتح
                    Color.FromArgb(30, 132, 199), // لون داكن
                    LinearGradientMode.Vertical))
                {
                    graphics.FillEllipse(brush, 0, 0, profilePicture.Width - 1, profilePicture.Height - 1);
                }

                // إضافة حدود لامعة
                using (var pen = new Pen(Color.FromArgb(200, 255, 255, 255), 2))
                {
                    graphics.DrawEllipse(pen, 1, 1, profilePicture.Width - 3, profilePicture.Height - 3);
                }
            };

            return profilePanel;
        }

        // دالة إنشاء الأزرار المحسنة مع التأثيرات ثلاثية الأبعاد
        private Panel CreateModernMenuButton(string icon, Color color, int y, Action clickAction, string tooltip = "")
        {
            var buttonPanel = new Panel
            {
                Size = new Size(65, 65), // حجم أكبر
                Location = new Point(12, y), // موضع محسن
                BackColor = Color.Transparent,
                Cursor = Cursors.Hand,
                Tag = new { Color = color, Tooltip = tooltip } // حفظ اللون والتلميح
            };

            // إضافة الأيقونة المحسنة
            var iconLabel = new Label
            {
                Text = icon,
                Font = new Font("Segoe UI Emoji", 24F), // خط أكبر
                ForeColor = Color.White,
                Size = new Size(65, 65), // حجم أكبر
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent,
                Cursor = Cursors.Hand
            };

            buttonPanel.Controls.Add(iconLabel);

            // إضافة وظيفة النقر
            EventHandler clickHandler = (s, e) => clickAction?.Invoke();
            buttonPanel.Click += clickHandler;
            iconLabel.Click += clickHandler;

            // متغيرات للتأثيرات
            bool isPressed = false;
            bool isHovered = false;

            // تأثيرات التفاعل المحسنة
            EventHandler mouseEnter = (s, e) => {
                isHovered = true;
                buttonPanel.Invalidate();
            };

            EventHandler mouseLeave = (s, e) => {
                isHovered = false;
                isPressed = false;
                buttonPanel.Invalidate();
            };

            MouseEventHandler mouseDown = (s, e) => {
                isPressed = true;
                buttonPanel.Invalidate();
            };

            MouseEventHandler mouseUp = (s, e) => {
                isPressed = false;
                buttonPanel.Invalidate();
            };

            buttonPanel.MouseEnter += mouseEnter;
            buttonPanel.MouseLeave += mouseLeave;
            buttonPanel.MouseDown += mouseDown;
            buttonPanel.MouseUp += mouseUp;
            iconLabel.MouseEnter += mouseEnter;
            iconLabel.MouseLeave += mouseLeave;
            iconLabel.MouseDown += mouseDown;
            iconLabel.MouseUp += mouseUp;

            // رسم الزر مع التأثيرات ثلاثية الأبعاد المحسنة
            buttonPanel.Paint += (s, e) => {
                var graphics = e.Graphics;
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                // تحديد الألوان حسب الحالة
                Color baseColor = color;
                Color lightColor = ControlPaint.Light(baseColor, 0.3f);
                Color darkColor = ControlPaint.Dark(baseColor, 0.2f);

                if (isPressed)
                {
                    baseColor = ControlPaint.Dark(color, 0.1f);
                    lightColor = ControlPaint.Light(baseColor, 0.2f);
                    darkColor = ControlPaint.Dark(baseColor, 0.3f);
                }
                else if (isHovered)
                {
                    baseColor = ControlPaint.Light(color, 0.15f);
                    lightColor = ControlPaint.Light(baseColor, 0.4f);
                    darkColor = ControlPaint.Dark(baseColor, 0.1f);
                }

                int offset = isPressed ? 2 : 0;
                int shadowOffset = isPressed ? 1 : 3;

                // رسم الظل ثلاثي الأبعاد
                using (var shadowBrush = new SolidBrush(Color.FromArgb(100, 0, 0, 0)))
                {
                    graphics.FillEllipse(shadowBrush,
                        shadowOffset + offset, shadowOffset + offset,
                        buttonPanel.Width - shadowOffset - 2, buttonPanel.Height - shadowOffset - 2);
                }

                // رسم التدرج الأساسي
                using (var brush = new LinearGradientBrush(
                    new Rectangle(offset, offset, buttonPanel.Width - 4, buttonPanel.Height - 4),
                    lightColor, darkColor, LinearGradientMode.Vertical))
                {
                    graphics.FillEllipse(brush, offset, offset, buttonPanel.Width - 4, buttonPanel.Height - 4);
                }

                // إضافة لمعة علوية
                using (var glossBrush = new LinearGradientBrush(
                    new Rectangle(offset, offset, buttonPanel.Width - 4, (buttonPanel.Height - 4) / 2),
                    Color.FromArgb(80, 255, 255, 255), Color.FromArgb(20, 255, 255, 255),
                    LinearGradientMode.Vertical))
                {
                    graphics.FillEllipse(glossBrush, offset, offset, buttonPanel.Width - 4, (buttonPanel.Height - 4) / 2);
                }

                // إضافة حدود لامعة
                using (var pen = new Pen(Color.FromArgb(150, 255, 255, 255), 1.5f))
                {
                    graphics.DrawEllipse(pen, offset + 1, offset + 1, buttonPanel.Width - 6, buttonPanel.Height - 6);
                }
            };

            return buttonPanel;
        }

        // الاحتفاظ بالدالة القديمة للتوافق
        private Panel CreateCircularMenuButton(string icon, Color color, int y, Action clickAction)
        {
            return CreateModernMenuButton(icon, color, y, clickAction);
        }

        private void SetupModernUI()
        {
            this.SetStyle(ControlStyles.AllPaintingInWmPaint |
                         ControlStyles.UserPaint |
                         ControlStyles.DoubleBuffer, true);

            // تطبيق تأثيرات بصرية
            CreateTopControls();
            sidePanel.Paint += SidePanel_Paint;
        }

        // تم حذف TopPanel_Paint - لا حاجة لها بعد إزالة الشريط العلوي

        private void SidePanel_Paint(object sender, PaintEventArgs e)
        {
            var graphics = e.Graphics;
            graphics.SmoothingMode = SmoothingMode.AntiAlias;

            // التحقق من أن الأبعاد ليست صفراً لتجنب خطأ Rectangle
            if (sidePanel.ClientRectangle.Width > 0 && sidePanel.ClientRectangle.Height > 0)
            {
                // تدرج خلفية ثلاثي الأبعاد محسن
                using (var brush = new LinearGradientBrush(
                    sidePanel.ClientRectangle,
                    Color.FromArgb(52, 73, 94), // رمادي مزرق فاتح
                    Color.FromArgb(44, 62, 80), // رمادي مزرق داكن
                    LinearGradientMode.Horizontal))
                {
                    graphics.FillRectangle(brush, sidePanel.ClientRectangle);
                }

                // إضافة لمعة جانبية
                using (var glossBrush = new LinearGradientBrush(
                    new Rectangle(0, 0, sidePanel.Width / 3, sidePanel.Height),
                    Color.FromArgb(30, 255, 255, 255),
                    Color.FromArgb(5, 255, 255, 255),
                    LinearGradientMode.Horizontal))
                {
                    graphics.FillRectangle(glossBrush, 0, 0, sidePanel.Width / 3, sidePanel.Height);
                }

                // خط فاصل لامع على الجانب الأيمن
                using (var pen = new Pen(Color.FromArgb(100, 255, 255, 255), 1))
                {
                    graphics.DrawLine(pen, sidePanel.Width - 1, 0, sidePanel.Width - 1, sidePanel.Height);
                }

                // إضافة ظل داخلي
                using (var shadowPen = new Pen(Color.FromArgb(30, 0, 0, 0), 1))
                {
                    graphics.DrawLine(shadowPen, sidePanel.Width - 2, 0, sidePanel.Width - 2, sidePanel.Height);
                }
            }
        }

        // تم حذف SetupTimer وTimeTimer_Tick وUpdateDateTime - لا حاجة لها بعد إزالة الشريط العلوي

        private void LoadDashboard()
        {
            ClearContentPanel();

            // إنشاء البيانات التجريبية إذا لم تكن موجودة
            try
            {
                if (!SimpleDataManager.Instance.HasSampleData())
                {
                    SimpleDataManager.Instance.GenerateSampleData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء البيانات التجريبية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }

            ShowSimpleMessage("لوحة التحكم", "🎉 تم حذف نظام SQL بالكامل من الجذور!\n\nالنظام الآن يعمل بدون قاعدة بيانات SQL\n\n✅ البيانات التجريبية جاهزة للاستخدام");
        }

        private void LoadSalesModule()
        {
            ClearContentPanel();
            CreateSalesInterface();
        }

        // تم حذف LoadPurchasesModule - وحدة المشتريات محذوفة

        private void LoadInventoryModule()
        {
            ClearContentPanel();
            ShowSimpleMessage("المخزون", "✅ وحدة المخزون\n\nلا مزيد من مشاكل قاعدة البيانات!");
        }

        private void LoadCustomersModule()
        {
            ClearContentPanel();
            ShowSimpleMessage("العملاء", "✅ وحدة العملاء\n\nالنظام أصبح أبسط وأسرع!");
        }

        private void LoadSuppliersModule()
        {
            ClearContentPanel();
            ShowSimpleMessage("الموردين", "✅ وحدة الموردين\n\nتم حذف SQL بدقة ومن الجذور!");
        }

        private void LoadReportsModule()
        {
            ClearContentPanel();
            ShowSimpleMessage("التقارير", "✅ وحدة التقارير\n\nالنظام الجديد بدون SQL!");
        }

        private void LoadSettingsModule()
        {
            ClearContentPanel();
            ShowSimpleMessage("الإعدادات", "✅ إعدادات النظام\n\nتم التنظيف الكامل لنظام SQL!");
        }

        private void LogoutUser()
        {
            var result = MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد الخروج",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                CurrentUser.Instance.Logout();
                this.Hide();
                var loginForm = new LoginForm();
                loginForm.ShowDialog();
                this.Close();
            }
        }

        private void ClearContentPanel()
        {
            contentPanel.Controls.Clear();
        }

        private void ShowSimpleMessage(string title, string message)
        {
            var messagePanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(248, 251, 255), // خلفية أكثر نعومة
                Padding = new Padding(60) // تباعد أكبر
            };

            // إضافة تأثيرات ثلاثية الأبعاد للوحة
            messagePanel.Paint += (s, e) => {
                var graphics = e.Graphics;
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                // رسم خلفية متدرجة
                using (var brush = new LinearGradientBrush(
                    messagePanel.ClientRectangle,
                    Color.FromArgb(255, 255, 255),
                    Color.FromArgb(248, 251, 255),
                    LinearGradientMode.Vertical))
                {
                    graphics.FillRectangle(brush, messagePanel.ClientRectangle);
                }

                // إضافة ظل داخلي خفيف
                using (var shadowBrush = new LinearGradientBrush(
                    new Rectangle(0, 0, messagePanel.Width, 20),
                    Color.FromArgb(20, 0, 0, 0),
                    Color.FromArgb(0, 0, 0, 0),
                    LinearGradientMode.Vertical))
                {
                    graphics.FillRectangle(shadowBrush, 0, 0, messagePanel.Width, 20);
                }
            };

            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 28F, FontStyle.Bold), // خط أكبر وأكثر عصرية
                ForeColor = Color.FromArgb(41, 128, 185), // لون أزرق محسن
                Dock = DockStyle.Top,
                Height = 80, // ارتفاع أكبر
                TextAlign = ContentAlignment.MiddleCenter
            };

            var messageLabel = new Label
            {
                Text = message,
                Font = new Font("Segoe UI", 16F, FontStyle.Regular), // خط أكبر
                ForeColor = Color.FromArgb(85, 85, 85), // لون رمادي محسن
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter
            };

            messagePanel.Controls.AddRange(new Control[] { messageLabel, titleLabel });
            contentPanel.Controls.Add(messagePanel);
        }

        #region Sales Interface

        // دالة إنشاء أيقونات ثلاثية الأبعاد محسنة للمبيعات (مبسطة للتوافق)
        private Button Create3DIconButton(string text, string icon, Color baseColor, Point location, Size size, EventHandler clickHandler)
        {
            var button = new Button
            {
                Text = $"{icon} {text}",
                Font = new Font("Tahoma", 11F, FontStyle.Bold),
                ForeColor = Color.White,
                Location = location,
                Size = size,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                UseVisualStyleBackColor = false,
                BackColor = baseColor
            };

            button.FlatAppearance.BorderSize = 0;

            // تأثيرات hover مبسطة
            button.MouseEnter += (s, e) => {
                button.BackColor = ControlPaint.Light(baseColor, 0.2f);
            };

            button.MouseLeave += (s, e) => {
                button.BackColor = baseColor;
            };

            button.MouseDown += (s, e) => {
                button.BackColor = ControlPaint.Dark(baseColor, 0.1f);
            };

            button.MouseUp += (s, e) => {
                button.BackColor = ControlPaint.Light(baseColor, 0.2f);
            };

            if (clickHandler != null)
                button.Click += clickHandler;

            return button;
        }

        private void ShowSalesInterface()
        {
            CreateSalesInterface();
        }

        private void CreateSalesInterface()
        {
            // إنشاء اللوحة الرئيسية للمبيعات (بدون عنوان لتوفير مساحة أكبر)
            var salesPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(245, 245, 245),
                Padding = new Padding(10) // تقليل الحشو لتوفير مساحة أكبر
            };

            // لوحة الفاتورة الحالية (تستغل المساحة الكاملة بعد حذف العنوان)
            var currentInvoicePanel = CreateCurrentInvoicePanel();
            currentInvoicePanel.Location = new Point(10, 10); // بدء من الأعلى مباشرة
            currentInvoicePanel.Dock = DockStyle.Fill; // استغلال المساحة الكاملة

            // إضافة لوحة الفاتورة فقط (بدون عنوان)
            salesPanel.Controls.Add(currentInvoicePanel);

            contentPanel.Controls.Add(salesPanel);
        }

        private Panel CreateQuickSearchPanel()
        {
            var panel = new Panel
            {
                BackColor = Color.FromArgb(248, 249, 250),
                BorderStyle = BorderStyle.None,
                Padding = new Padding(10, 5, 10, 5)
            };

            // إضافة تأثير بصري ثلاثي الأبعاد مطابق لمربع العميل
            panel.Paint += (s, e) =>
            {
                if (panel.Width > 10 && panel.Height > 10)
                {
                    var graphics = e.Graphics;
                    graphics.SmoothingMode = SmoothingMode.AntiAlias;

                    // رسم خلفية متدرجة
                    using (var brush = new LinearGradientBrush(panel.ClientRectangle,
                        Color.FromArgb(255, 255, 255), Color.FromArgb(248, 249, 250), LinearGradientMode.Vertical))
                    {
                        graphics.FillRectangle(brush, panel.ClientRectangle);
                    }

                    // رسم حدود لامعة
                    using (var pen = new Pen(Color.FromArgb(52, 152, 219), 2))
                    {
                        graphics.DrawRectangle(pen, 0, 0, panel.Width - 1, panel.Height - 1);
                    }

                    // إضافة ظل داخلي
                    using (var shadowBrush = new LinearGradientBrush(
                        new Rectangle(0, 0, panel.Width, 5),
                        Color.FromArgb(30, 0, 0, 0), Color.FromArgb(0, 0, 0, 0), LinearGradientMode.Vertical))
                    {
                        graphics.FillRectangle(shadowBrush, 0, 0, panel.Width, 5);
                    }
                }
            };

            // الصف الأول: أيقونة وتسمية البحث
            var topRow = new Panel
            {
                Dock = DockStyle.Top,
                Height = 30,
                BackColor = Color.Transparent
            };

            var searchIcon = new Label
            {
                Text = "🔍",
                Font = new Font("Segoe UI Emoji", 14F),
                Dock = DockStyle.Left,
                Width = 30,
                TextAlign = ContentAlignment.MiddleCenter
            };

            var searchLabel = new Label
            {
                Text = "البحث السريع:",
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Dock = DockStyle.Left,
                Width = 100,
                TextAlign = ContentAlignment.MiddleLeft
            };

            topRow.Controls.AddRange(new Control[] { searchIcon, searchLabel });

            // الصف الثاني: مربع البحث وزر البحث المتقدم
            var bottomRow = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.Transparent,
                Padding = new Padding(0, 5, 0, 0)
            };

            var searchTextBox = new TextBox
            {
                Name = "txtQuickSearch",
                Font = new Font("Segoe UI", 11F),
                Dock = DockStyle.Left,
                Width = 250,
                PlaceholderText = "اكتب الباركود أو الكود واضغط Enter...",
                BackColor = Color.White,
                ForeColor = Color.FromArgb(52, 73, 94),
                BorderStyle = BorderStyle.FixedSingle
            };

            var advancedSearchBtn = new Button
            {
                Text = "🔍 بحث متقدم",
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Dock = DockStyle.Right,
                Width = 120,
                Cursor = Cursors.Hand,
                Margin = new Padding(5, 0, 0, 0)
            };
            advancedSearchBtn.FlatAppearance.BorderSize = 0;

            // إضافة تأثيرات hover للزر
            advancedSearchBtn.MouseEnter += (s, e) => advancedSearchBtn.BackColor = Color.FromArgb(41, 128, 185);
            advancedSearchBtn.MouseLeave += (s, e) => advancedSearchBtn.BackColor = Color.FromArgb(52, 152, 219);

            bottomRow.Controls.AddRange(new Control[] { advancedSearchBtn, searchTextBox });

            // أحداث البحث مع تحسينات بصرية
            searchTextBox.KeyDown += QuickSearch_KeyDown;
            searchTextBox.Enter += (s, e) => {
                searchTextBox.BackColor = Color.FromArgb(240, 248, 255);
                searchTextBox.BorderStyle = BorderStyle.FixedSingle;
            };
            searchTextBox.Leave += (s, e) => {
                searchTextBox.BackColor = Color.White;
            };

            advancedSearchBtn.Click += AdvancedSearch_Click;

            panel.Controls.AddRange(new Control[] { bottomRow, topRow });

            return panel;
        }

        private Panel CreateCurrentInvoicePanel()
        {
            // استغلال المساحة الكاملة بعد حذف العنوان الرئيسي
            var panel = new Panel
            {
                Dock = DockStyle.Fill, // استغلال المساحة الكاملة
                BackColor = Color.White,
                BorderStyle = BorderStyle.None,
                Padding = new Padding(10) // حشو مناسب
            };

            // إضافة حدود وظل مخصص
            panel.Paint += (s, e) =>
            {
                if (panel.Width > 10 && panel.Height > 10)
                {
                    // رسم الظل
                    var shadowRect = new Rectangle(3, 3, panel.Width - 3, panel.Height - 3);
                    e.Graphics.FillRectangle(new SolidBrush(Color.FromArgb(50, 0, 0, 0)), shadowRect);

                    // رسم الحدود الرئيسية
                    var rect = new Rectangle(0, 0, panel.Width - 4, panel.Height - 4);
                    e.Graphics.FillRectangle(Brushes.White, rect);
                    e.Graphics.DrawRectangle(new Pen(Color.FromArgb(52, 152, 219), 2), rect);
                }
            };

            // شريط العنوان المحسن - يستخدم Dock
            var titlePanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 50,
                BackColor = Color.FromArgb(52, 152, 219),
                Padding = new Padding(20, 12, 20, 8)
            };

            var invoiceLabel = new Label
            {
                Text = "🧾 الفاتورة الحالية",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                Dock = DockStyle.Left,
                Width = 250,
                TextAlign = ContentAlignment.MiddleLeft
            };

            var invoiceNumberLabel = new Label
            {
                Text = "رقم الفاتورة: جديدة",
                Font = new Font("Segoe UI", 12F, FontStyle.Regular),
                ForeColor = Color.White,
                Dock = DockStyle.Right,
                Width = 200,
                TextAlign = ContentAlignment.MiddleRight
            };

            titlePanel.Controls.AddRange(new Control[] { invoiceLabel, invoiceNumberLabel });

            // الصف العلوي: مربع البحث السريع ومربع بحث العميل - يستخدم Dock
            var topRowPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = Color.Transparent,
                Padding = new Padding(5)
            };

            // مربع البحث السريع للمنتجات (يسار)
            var quickSearchPanel = CreateQuickSearchPanel();
            quickSearchPanel.Dock = DockStyle.Left;
            quickSearchPanel.Width = 400; // عرض ثابت مناسب

            // معلومات العميل في الجانب الأيمن
            var customerPanel = CreateCompactCustomerInfoPanel();
            customerPanel.Dock = DockStyle.Right;
            customerPanel.Width = 350; // عرض ثابت مناسب

            topRowPanel.Controls.AddRange(new Control[] { customerPanel, quickSearchPanel });

            // الصف السفلي: المجاميع والأزرار - يستخدم Dock
            var bottomRowPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 100,
                BackColor = Color.Transparent,
                Padding = new Padding(5)
            };

            // لوحة المجاميع في الجانب الأيمن
            var totalsPanel = CreateEnhancedTotalsPanel();
            totalsPanel.Dock = DockStyle.Right;
            totalsPanel.Width = 300;

            // أزرار العمليات في الجانب الأيسر
            var actionsPanel = CreateEnhancedActionsPanel();
            actionsPanel.Dock = DockStyle.Left;
            actionsPanel.Width = 500;

            bottomRowPanel.Controls.AddRange(new Control[] { totalsPanel, actionsPanel });

            // المنطقة الوسطى: جدول المنتجات (يملأ المساحة المتبقية)
            var productsGrid = CreateEnhancedProductsGrid();
            productsGrid.Dock = DockStyle.Fill; // يملأ المساحة المتبقية تلقائياً
            productsGrid.Margin = new Padding(5);

            // إضافة العناصر بالترتيب الصحيح (من الأعلى للأسفل)
            panel.Controls.Add(productsGrid); // أولاً (سيكون في الوسط)
            panel.Controls.Add(bottomRowPanel); // ثانياً (سيكون في الأسفل)
            panel.Controls.Add(topRowPanel); // ثالثاً (سيكون في الأعلى)
            panel.Controls.Add(titlePanel); // أخيراً (سيكون في أعلى الكل)

            return panel;
        }

        // دالة إنشاء لوحة معلومات العميل المدمجة للجانب الأيمن (محاذية لمربع البحث السريع)
        private Panel CreateCompactCustomerInfoPanel()
        {
            var panel = new Panel
            {
                BackColor = Color.FromArgb(248, 249, 250),
                BorderStyle = BorderStyle.None,
                Padding = new Padding(10, 5, 10, 5)
            };

            // إضافة تأثير بصري ثلاثي الأبعاد
            panel.Paint += (s, e) =>
            {
                if (panel.Width > 10 && panel.Height > 10)
                {
                    var graphics = e.Graphics;
                    graphics.SmoothingMode = SmoothingMode.AntiAlias;

                    // رسم خلفية متدرجة
                    using (var brush = new LinearGradientBrush(panel.ClientRectangle,
                        Color.FromArgb(255, 255, 255), Color.FromArgb(248, 249, 250), LinearGradientMode.Vertical))
                    {
                        graphics.FillRectangle(brush, panel.ClientRectangle);
                    }

                    // رسم حدود لامعة
                    using (var pen = new Pen(Color.FromArgb(52, 152, 219), 2))
                    {
                        graphics.DrawRectangle(pen, 0, 0, panel.Width - 1, panel.Height - 1);
                    }

                    // إضافة ظل داخلي
                    using (var shadowBrush = new LinearGradientBrush(
                        new Rectangle(0, 0, panel.Width, 5),
                        Color.FromArgb(30, 0, 0, 0), Color.FromArgb(0, 0, 0, 0), LinearGradientMode.Vertical))
                    {
                        graphics.FillRectangle(shadowBrush, 0, 0, panel.Width, 5);
                    }
                }
            };

            // الصف الأول: أيقونة وتسمية العميل
            var topRow = new Panel
            {
                Dock = DockStyle.Top,
                Height = 30,
                BackColor = Color.Transparent
            };

            var customerIcon = new Label
            {
                Text = "👤",
                Font = new Font("Segoe UI Emoji", 14F),
                Dock = DockStyle.Left,
                Width = 30,
                TextAlign = ContentAlignment.MiddleCenter
            };

            var customerLabel = new Label
            {
                Text = "العميل:",
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Dock = DockStyle.Left,
                Width = 60,
                TextAlign = ContentAlignment.MiddleLeft
            };

            topRow.Controls.AddRange(new Control[] { customerIcon, customerLabel });

            // الصف الثاني: مربع البحث والقائمة المنسدلة
            var bottomRow = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.Transparent,
                Padding = new Padding(0, 5, 0, 0)
            };

            var customerSearchBox = new TextBox
            {
                Name = "txtCustomerSearch",
                Font = new Font("Segoe UI", 9F),
                Dock = DockStyle.Left,
                Width = 140,
                PlaceholderText = "ابحث عن العميل واضغط Enter...",
                BackColor = Color.White,
                ForeColor = Color.FromArgb(52, 73, 94),
                BorderStyle = BorderStyle.FixedSingle
            };

            // إضافة تأثيرات بصرية لمربع البحث عن العميل
            customerSearchBox.Enter += (s, e) => {
                customerSearchBox.BackColor = Color.FromArgb(240, 248, 255);
                customerSearchBox.BorderStyle = BorderStyle.FixedSingle;
            };
            customerSearchBox.Leave += (s, e) => {
                customerSearchBox.BackColor = Color.White;
            };

            var customerCombo = new ComboBox
            {
                Name = "cmbCustomer",
                Font = new Font("Segoe UI", 9F),
                Dock = DockStyle.Left,
                Width = 120,
                DropDownStyle = ComboBoxStyle.DropDownList,
                FlatStyle = FlatStyle.Flat,
                Margin = new Padding(5, 0, 0, 0)
            };

            // زر البحث المتقدم للعملاء
            var customerSearchBtn = new Button
            {
                Text = "🔍",
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Dock = DockStyle.Right,
                Width = 30,
                Cursor = Cursors.Hand,
                Margin = new Padding(2, 0, 0, 0)
            };
            customerSearchBtn.FlatAppearance.BorderSize = 0;

            // إضافة تأثيرات hover للزر
            customerSearchBtn.MouseEnter += (s, e) => customerSearchBtn.BackColor = Color.FromArgb(41, 128, 185);
            customerSearchBtn.MouseLeave += (s, e) => customerSearchBtn.BackColor = Color.FromArgb(52, 152, 219);
            customerSearchBtn.Click += SearchCustomer_Click;

            bottomRow.Controls.AddRange(new Control[] { customerSearchBtn, customerCombo, customerSearchBox });

            // تحميل العملاء
            LoadCustomersToCombo(customerCombo);

            // أحداث البحث
            customerSearchBox.KeyDown += CustomerSearch_KeyDown;
            customerSearchBox.TextChanged += CustomerSearch_TextChanged;

            panel.Controls.AddRange(new Control[] { bottomRow, topRow });

            return panel;
        }

        private Panel CreateEnhancedCustomerInfoPanel()
        {
            var panel = new Panel
            {
                Size = new Size(contentPanel.Width - 30, 90), // استغلال العرض الكامل تقريباً
                BackColor = Color.FromArgb(248, 249, 250),
                BorderStyle = BorderStyle.FixedSingle
            };

            // إضافة تأثير بصري
            panel.Paint += (s, e) =>
            {
                var rect = new Rectangle(0, 0, panel.Width - 1, panel.Height - 1);
                e.Graphics.DrawRectangle(new Pen(Color.FromArgb(200, 200, 200), 1), rect);
            };

            var customerIcon = new Label
            {
                Text = "👤",
                Font = new Font("Segoe UI Emoji", 16F),
                Location = new Point(15, 18),
                Size = new Size(30, 25),
                TextAlign = ContentAlignment.MiddleCenter
            };

            var customerLabel = new Label
            {
                Text = "العميل:",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(55, 20),
                Size = new Size(60, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // حساب العرض المتاح للعناصر
            var availableWidth = panel.Width - 140; // ترك مساحة للأيقونة والتسمية
            var searchBoxWidth = (int)(availableWidth * 0.35); // 35% للبحث
            var comboWidth = (int)(availableWidth * 0.35); // 35% للقائمة المنسدلة
            var buttonWidth = (int)(availableWidth * 0.1); // 10% لكل زر

            // مربع البحث عن العميل
            var customerSearchBox = new TextBox
            {
                Name = "txtCustomerSearch",
                Font = new Font("Tahoma", 11F),
                Location = new Point(125, 18),
                Size = new Size(searchBoxWidth, 25),
                PlaceholderText = "ابحث عن العميل بالاسم أو الهاتف...",
                BackColor = Color.White,
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            var customerCombo = new ComboBox
            {
                Name = "cmbCustomer",
                Font = new Font("Tahoma", 11F),
                Location = new Point(125, 50),
                Size = new Size(comboWidth, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                FlatStyle = FlatStyle.Flat
            };

            // حساب مواضع الأزرار بناءً على العرض المتاح
            var firstButtonX = 125 + Math.Max(searchBoxWidth, comboWidth) + 10;
            var buttonSpacing = buttonWidth + 10;

            var searchCustomerBtn = new Button
            {
                Text = "🔍 بحث",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                Location = new Point(firstButtonX, 18),
                Size = new Size(buttonWidth, 25),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            searchCustomerBtn.FlatAppearance.BorderSize = 0;

            var newCustomerBtn = new Button
            {
                Text = "➕ جديد",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                Location = new Point(firstButtonX + buttonSpacing, 18),
                Size = new Size(buttonWidth, 25),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            newCustomerBtn.FlatAppearance.BorderSize = 0;

            var customerInfoBtn = new Button
            {
                Text = "ℹ️ معلومات",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                Location = new Point(firstButtonX + (buttonSpacing * 2), 18),
                Size = new Size(buttonWidth, 25),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            customerInfoBtn.FlatAppearance.BorderSize = 0;

            // تحميل العملاء
            LoadCustomersToCombo(customerCombo);

            // أحداث البحث
            customerSearchBox.KeyDown += CustomerSearch_KeyDown;
            customerSearchBox.TextChanged += CustomerSearch_TextChanged;
            searchCustomerBtn.Click += SearchCustomer_Click;
            newCustomerBtn.Click += NewCustomer_Click;
            customerInfoBtn.Click += CustomerInfo_Click;

            panel.Controls.AddRange(new Control[] {
                customerIcon, customerLabel, customerSearchBox, customerCombo,
                searchCustomerBtn, newCustomerBtn, customerInfoBtn
            });

            return panel;
        }

        private Panel CreateCustomerInfoPanel()
        {
            var panel = new Panel
            {
                Size = new Size(500, 50),
                BackColor = Color.FromArgb(248, 249, 250)
            };

            var customerLabel = new Label
            {
                Text = "العميل:",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                Location = new Point(10, 15),
                Size = new Size(50, 20)
            };

            var customerCombo = new ComboBox
            {
                Name = "cmbCustomer",
                Font = new Font("Tahoma", 10F),
                Location = new Point(70, 12),
                Size = new Size(200, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // تحميل العملاء
            LoadCustomersToCombo(customerCombo);

            var newCustomerBtn = new Button
            {
                Text = "عميل جديد",
                Font = new Font("Tahoma", 9F),
                Location = new Point(280, 12),
                Size = new Size(80, 25),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            newCustomerBtn.FlatAppearance.BorderSize = 0;

            panel.Controls.AddRange(new Control[] {
                customerLabel,
                customerCombo,
                newCustomerBtn
            });

            return panel;
        }

        private DataGridView CreateEnhancedProductsGrid()
        {
            var grid = new DataGridView
            {
                Name = "dgvProducts",
                Size = new Size(contentPanel.Width - 70, 300), // حجم افتراضي، سيتم تعديله ديناميكياً
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = false,
                Font = new Font("Tahoma", 11F),
                RowHeadersVisible = false,
                EnableHeadersVisualStyles = false,
                GridColor = Color.FromArgb(230, 230, 230),
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                RowTemplate = { Height = 40 }, // زيادة ارتفاع الصفوف قليلاً
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill, // ملء العرض المتاح
                ScrollBars = ScrollBars.Both, // إضافة شريط التمرير عند الحاجة
                Dock = DockStyle.None
            };

            // تنسيق رأس الجدول
            grid.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            grid.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            grid.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            grid.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            grid.ColumnHeadersHeight = 40;

            // تنسيق الصفوف
            grid.DefaultCellStyle.BackColor = Color.White;
            grid.DefaultCellStyle.ForeColor = Color.FromArgb(52, 73, 94);
            grid.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219);
            grid.DefaultCellStyle.SelectionForeColor = Color.White;
            grid.DefaultCellStyle.Padding = new Padding(5);

            // تنسيق الصفوف المتناوبة
            grid.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);

            // إضافة الأعمدة مع تحسينات وعرض متكيف
            var productNameColumn = new DataGridViewTextBoxColumn
            {
                Name = "ProductName",
                HeaderText = "📦 اسم المنتج",
                FillWeight = 40, // 40% من العرض المتاح
                ReadOnly = true,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleLeft,
                    Font = new Font("Tahoma", 11F, FontStyle.Bold),
                    Padding = new Padding(8, 5, 5, 5)
                }
            };

            var quantityColumn = new DataGridViewTextBoxColumn
            {
                Name = "Quantity",
                HeaderText = "📊 الكمية",
                FillWeight = 12, // 12% من العرض المتاح
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Format = "N0",
                    BackColor = Color.FromArgb(255, 248, 220),
                    Font = new Font("Tahoma", 11F, FontStyle.Bold)
                }
            };

            var priceColumn = new DataGridViewTextBoxColumn
            {
                Name = "Price",
                HeaderText = "💰 السعر",
                FillWeight = 15, // 15% من العرض المتاح
                ReadOnly = true,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleRight,
                    Format = "N2",
                    ForeColor = Color.FromArgb(46, 204, 113),
                    Font = new Font("Tahoma", 11F, FontStyle.Bold)
                }
            };

            var discountColumn = new DataGridViewTextBoxColumn
            {
                Name = "Discount",
                HeaderText = "🏷️ الخصم %",
                FillWeight = 12, // 12% من العرض المتاح
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Format = "N1",
                    BackColor = Color.FromArgb(255, 248, 220),
                    Font = new Font("Tahoma", 11F)
                }
            };

            var totalColumn = new DataGridViewTextBoxColumn
            {
                Name = "Total",
                HeaderText = "💵 المجموع",
                FillWeight = 18, // 18% من العرض المتاح
                ReadOnly = true,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleRight,
                    Format = "N2",
                    Font = new Font("Tahoma", 12F, FontStyle.Bold),
                    ForeColor = Color.FromArgb(52, 152, 219)
                }
            };

            // إضافة عمود الحذف
            var deleteColumn = new DataGridViewButtonColumn
            {
                Name = "Delete",
                HeaderText = "🗑️ حذف",
                FillWeight = 8, // 8% من العرض المتاح
                Text = "❌",
                UseColumnTextForButtonValue = true,
                DefaultCellStyle = {
                    BackColor = Color.FromArgb(231, 76, 60),
                    ForeColor = Color.White,
                    Font = new Font("Tahoma", 10F, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                }
            };

            grid.Columns.AddRange(new DataGridViewColumn[] {
                productNameColumn, quantityColumn, priceColumn, discountColumn, totalColumn, deleteColumn
            });

            // أحداث الجدول
            grid.CellValueChanged += ProductsGrid_CellValueChanged;
            grid.KeyDown += ProductsGrid_KeyDown;
            grid.CellContentClick += ProductsGrid_CellContentClick;

            return grid;
        }

        private DataGridView CreateProductsGrid()
        {
            // استخدام النسخة المحسنة
            return CreateEnhancedProductsGrid();
        }

        private Panel CreateEnhancedTotalsPanel()
        {
            var panel = new Panel
            {
                Size = new Size(260, 90),
                BackColor = Color.White,
                BorderStyle = BorderStyle.None
            };

            // إضافة حدود وظل
            panel.Paint += (s, e) =>
            {
                // رسم الظل
                var shadowRect = new Rectangle(2, 2, panel.Width - 2, panel.Height - 2);
                e.Graphics.FillRectangle(new SolidBrush(Color.FromArgb(30, 0, 0, 0)), shadowRect);

                // رسم الخلفية والحدود
                var rect = new Rectangle(0, 0, panel.Width - 3, panel.Height - 3);
                e.Graphics.FillRectangle(new SolidBrush(Color.FromArgb(248, 249, 250)), rect);
                e.Graphics.DrawRectangle(new Pen(Color.FromArgb(52, 152, 219), 2), rect);
            };

            // أيقونة المجاميع
            var calculatorIcon = new Label
            {
                Text = "🧮",
                Font = new Font("Segoe UI Emoji", 14F),
                Location = new Point(10, 8),
                Size = new Size(25, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            var subtotalLabel = new Label
            {
                Text = "المجموع الفرعي:",
                Font = new Font("Tahoma", 11F, FontStyle.Regular),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(40, 10),
                Size = new Size(110, 20)
            };

            var subtotalValue = new Label
            {
                Name = "lblSubtotal",
                Text = "0.00 ريال",
                Font = new Font("Tahoma", 11F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(155, 10),
                Size = new Size(100, 20),
                TextAlign = ContentAlignment.MiddleRight
            };

            var taxLabel = new Label
            {
                Text = "الضريبة (15%):",
                Font = new Font("Tahoma", 11F, FontStyle.Regular),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(40, 35),
                Size = new Size(110, 20)
            };

            var taxValue = new Label
            {
                Name = "lblTax",
                Text = "0.00 ريال",
                Font = new Font("Tahoma", 11F, FontStyle.Bold),
                ForeColor = Color.FromArgb(243, 156, 18),
                Location = new Point(155, 35),
                Size = new Size(100, 20),
                TextAlign = ContentAlignment.MiddleRight
            };

            // خط فاصل
            var separatorPanel = new Panel
            {
                Location = new Point(10, 58),
                Size = new Size(240, 2),
                BackColor = Color.FromArgb(52, 152, 219)
            };

            var totalLabel = new Label
            {
                Text = "💰 المجموع الكلي:",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(46, 204, 113),
                Location = new Point(10, 65),
                Size = new Size(140, 20)
            };

            var totalValue = new Label
            {
                Name = "lblTotal",
                Text = "0.00 ريال",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(46, 204, 113),
                Location = new Point(155, 65),
                Size = new Size(100, 20),
                TextAlign = ContentAlignment.MiddleRight
            };

            panel.Controls.AddRange(new Control[] {
                calculatorIcon, subtotalLabel, subtotalValue,
                taxLabel, taxValue, separatorPanel,
                totalLabel, totalValue
            });

            return panel;
        }

        private Panel CreateTotalsPanel()
        {
            // استخدام النسخة المحسنة
            return CreateEnhancedTotalsPanel();
        }

        private Panel CreateEnhancedActionsPanel()
        {
            var panel = new Panel
            {
                Size = new Size(500, 90),
                BackColor = Color.Transparent
            };

            // أزرار العمليات المحسنة مع تصميم ثلاثي الأبعاد
            var saveBtn = Create3DIconButton("حفظ الفاتورة", "💾", Color.FromArgb(46, 204, 113),
                new Point(0, 15), new Size(140, 45), SaveInvoice_Click);

            var printBtn = Create3DIconButton("طباعة", "🖨️", Color.FromArgb(52, 152, 219),
                new Point(150, 15), new Size(110, 45), PrintInvoice_Click);

            var clearBtn = Create3DIconButton("مسح الكل", "🗑️", Color.FromArgb(231, 76, 60),
                new Point(270, 15), new Size(110, 45), ClearInvoice_Click);

            var previewBtn = Create3DIconButton("معاينة", "👁️", Color.FromArgb(155, 89, 182),
                new Point(390, 15), new Size(100, 45), PreviewInvoice_Click);

            // معلومات الحالة مع تصميم محسن
            var statusLabel = new Label
            {
                Name = "lblInvoiceStatus",
                Text = "جاهز لإضافة المنتجات...",
                Font = new Font("Segoe UI", 10F, FontStyle.Regular),
                ForeColor = Color.FromArgb(100, 100, 100),
                Location = new Point(0, 65),
                Size = new Size(400, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // لا حاجة لإضافة أحداث الأزرار هنا - تم إضافتها في Create3DIconButton

            panel.Controls.AddRange(new Control[] {
                saveBtn, printBtn, clearBtn, previewBtn, statusLabel
            });

            return panel;
        }

        private Panel CreateActionsPanel()
        {
            // استخدام النسخة المحسنة
            return CreateEnhancedActionsPanel();
        }

        #endregion

        #region Sales Events

        private void QuickSearch_KeyDown(object? sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                var textBox = sender as TextBox;
                if (textBox != null)
                {
                    var searchText = textBox.Text.Trim();

                    if (!string.IsNullOrEmpty(searchText))
                    {
                        // تأثير بصري أثناء البحث
                        textBox.BackColor = Color.FromArgb(255, 248, 220);
                        textBox.Enabled = false;

                        // تشغيل البحث
                        SearchAndAddProductWithAnimation(searchText, textBox);
                    }
                    else
                    {
                        // تأثير بصري للخطأ
                        FlashTextBoxError(textBox);
                    }
                }
            }
        }

        private void AdvancedSearch_Click(object sender, EventArgs e)
        {
            ShowProductSearchDialog();
        }

        private void SearchAndAddProduct(string searchText)
        {
            try
            {
                var products = SimpleDataManager.Instance.GetAllProducts();
                var product = products.FirstOrDefault(p =>
                    p.Code.Equals(searchText, StringComparison.OrdinalIgnoreCase) ||
                    p.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase));

                if (product != null)
                {
                    AddProductToInvoice(product);
                }
                else
                {
                    MessageBox.Show($"لم يتم العثور على منتج بالكود: {searchText}", "منتج غير موجود",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void SearchAndAddProductWithAnimation(string searchText, TextBox textBox)
        {
            try
            {
                var products = SimpleDataManager.Instance.GetAllProducts();
                var product = products.FirstOrDefault(p =>
                    p.Code.Equals(searchText, StringComparison.OrdinalIgnoreCase) ||
                    p.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase));

                // محاكاة تأخير البحث للتأثير البصري
                await Task.Delay(300);

                if (product != null)
                {
                    // تأثير النجاح
                    textBox.BackColor = Color.FromArgb(212, 237, 218);
                    AddProductToInvoice(product);

                    // رسالة نجاح مؤقتة
                    ShowTemporaryMessage($"✅ تم إضافة: {product.Name}", Color.FromArgb(46, 204, 113));

                    await Task.Delay(500);
                    textBox.Clear();
                }
                else
                {
                    // تأثير الفشل
                    textBox.BackColor = Color.FromArgb(248, 215, 218);
                    ShowTemporaryMessage($"❌ لم يتم العثور على: {searchText}", Color.FromArgb(231, 76, 60));
                    await Task.Delay(1000);
                }
            }
            catch (Exception ex)
            {
                textBox.BackColor = Color.FromArgb(248, 215, 218);
                ShowTemporaryMessage($"❌ خطأ في البحث: {ex.Message}", Color.FromArgb(231, 76, 60));
                await Task.Delay(1000);
            }
            finally
            {
                // إعادة تعيين حالة مربع النص
                textBox.BackColor = Color.White;
                textBox.Enabled = true;
                textBox.Focus();
            }
        }

        private async void FlashTextBoxError(TextBox textBox)
        {
            var originalColor = textBox.BackColor;

            // وميض أحمر
            for (int i = 0; i < 3; i++)
            {
                textBox.BackColor = Color.FromArgb(248, 215, 218);
                await Task.Delay(150);
                textBox.BackColor = originalColor;
                await Task.Delay(150);
            }

            ShowTemporaryMessage("⚠️ يرجى إدخال كود أو اسم المنتج", Color.FromArgb(243, 156, 18));
        }

        private async void FlashTextBoxErrorForCustomer(TextBox textBox)
        {
            var originalColor = textBox.BackColor;

            // وميض أحمر
            for (int i = 0; i < 3; i++)
            {
                textBox.BackColor = Color.FromArgb(248, 215, 218);
                await Task.Delay(150);
                textBox.BackColor = originalColor;
                await Task.Delay(150);
            }

            ShowTemporaryMessageForCustomer("⚠️ يرجى إدخال اسم أو هاتف العميل", Color.FromArgb(243, 156, 18));
        }

        private async void ShowTemporaryMessage(string message, Color color)
        {
            // البحث عن مكان لعرض الرسالة
            var searchPanel = contentPanel.Controls.Find("txtQuickSearch", true).FirstOrDefault()?.Parent;
            if (searchPanel != null)
            {
                var messageLabel = new Label
                {
                    Text = message,
                    Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                    ForeColor = Color.White,
                    BackColor = color,
                    AutoSize = false,
                    Size = new Size(300, 25),
                    TextAlign = ContentAlignment.MiddleCenter,
                    Location = new Point(10, searchPanel.Height - 30),
                    BorderStyle = BorderStyle.FixedSingle
                };

                searchPanel.Controls.Add(messageLabel);
                messageLabel.BringToFront();

                // إزالة الرسالة بعد 3 ثوان
                await Task.Delay(3000);

                if (searchPanel.Controls.Contains(messageLabel))
                {
                    searchPanel.Controls.Remove(messageLabel);
                    messageLabel.Dispose();
                }
            }
        }

        private async void ShowTemporaryMessageForCustomer(string message, Color color)
        {
            // البحث عن مكان لعرض الرسالة في مربع العميل
            var customerPanel = contentPanel.Controls.Find("txtCustomerSearch", true).FirstOrDefault()?.Parent;
            if (customerPanel != null)
            {
                var messageLabel = new Label
                {
                    Text = message,
                    Font = new Font("Segoe UI", 8F, FontStyle.Bold),
                    ForeColor = Color.White,
                    BackColor = color,
                    AutoSize = false,
                    Size = new Size(250, 20),
                    TextAlign = ContentAlignment.MiddleCenter,
                    Location = new Point(10, customerPanel.Height - 25),
                    BorderStyle = BorderStyle.FixedSingle
                };

                customerPanel.Controls.Add(messageLabel);
                messageLabel.BringToFront();

                // إزالة الرسالة بعد 3 ثوان
                await Task.Delay(3000);

                if (customerPanel.Controls.Contains(messageLabel))
                {
                    customerPanel.Controls.Remove(messageLabel);
                    messageLabel.Dispose();
                }
            }
        }

        private void AddProductToInvoice(SimpleProduct product)
        {
            var grid = contentPanel.Controls.Find("dgvProducts", true).FirstOrDefault() as DataGridView;
            if (grid != null)
            {
                // التحقق من وجود المنتج مسبقاً
                foreach (DataGridViewRow row in grid.Rows)
                {
                    if (row.Cells["ProductName"].Value?.ToString() == product.Name)
                    {
                        // زيادة الكمية إذا كان المنتج موجود
                        var currentQty = Convert.ToInt32(row.Cells["Quantity"].Value ?? 0);
                        row.Cells["Quantity"].Value = currentQty + 1;
                        CalculateRowTotal(row);
                        UpdateTotals();
                        return;
                    }
                }

                // إضافة منتج جديد
                var newRow = grid.Rows[grid.Rows.Add()];
                newRow.Cells["ProductName"].Value = product.Name;
                newRow.Cells["Quantity"].Value = 1;
                newRow.Cells["Price"].Value = product.Price;
                newRow.Cells["Discount"].Value = 0;
                newRow.Cells["Total"].Value = product.Price;

                // تخزين معرف المنتج في Tag
                newRow.Tag = product.Id;

                UpdateTotals();
            }
        }

        private void ShowProductSearchDialog()
        {
            var searchForm = new Form
            {
                Text = "🔍 البحث المتقدم عن المنتجات",
                Size = new Size(1200, 800),
                StartPosition = FormStartPosition.CenterParent,
                Font = new Font("Tahoma", 10F),
                FormBorderStyle = FormBorderStyle.Sizable,
                MaximizeBox = true,
                MinimizeBox = true,
                BackColor = Color.FromArgb(245, 245, 245),
                WindowState = FormWindowState.Normal
            };

            // إضافة تأثير الظل للنافذة
            searchForm.Paint += (s, e) =>
            {
                var rect = new Rectangle(0, 0, searchForm.Width - 1, searchForm.Height - 1);
                e.Graphics.DrawRectangle(new Pen(Color.FromArgb(200, 200, 200), 2), rect);
            };

            // تم حذف الشريط الأزرق لإتاحة مساحة أكبر للمنتجات

            // لوحة البحث المحسنة - ارتفاع مقلل جداً بعد حذف شريط الإحصائيات
            var searchPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 55,
                BackColor = Color.White,
                Padding = new Padding(25, 10, 25, 10)
            };

            // إضافة حدود للوحة البحث
            searchPanel.Paint += (s, e) =>
            {
                var rect = new Rectangle(0, searchPanel.Height - 1, searchPanel.Width, 1);
                e.Graphics.FillRectangle(new SolidBrush(Color.FromArgb(230, 230, 230)), rect);
            };

            var searchLabel = new Label
            {
                Text = "🔍 البحث:",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(25, 15),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var searchBox = new TextBox
            {
                Font = new Font("Tahoma", 12F),
                Location = new Point(115, 15),
                Size = new Size(500, 25),
                PlaceholderText = "ابحث بالاسم، الكود، الفئة، أو أي معلومات أخرى...",
                BorderStyle = BorderStyle.FixedSingle
            };

            // تحسين مظهر مربع البحث
            searchBox.Paint += (s, e) =>
            {
                var rect = new Rectangle(0, 0, searchBox.Width - 1, searchBox.Height - 1);
                e.Graphics.DrawRectangle(new Pen(Color.FromArgb(52, 152, 219), 2), rect);
            };

            var clearBtn = new Button
            {
                Text = "🗑️ مسح",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(625, 15),
                Size = new Size(70, 25),
                Cursor = Cursors.Hand
            };
            clearBtn.FlatAppearance.BorderSize = 0;

            var refreshBtn = new Button
            {
                Text = "🔄 تحديث",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(705, 15),
                Size = new Size(80, 25),
                Cursor = Cursors.Hand
            };
            refreshBtn.FlatAppearance.BorderSize = 0;

            // تم حذف شريط الإحصائيات لتوفير مساحة أكبر للمنتجات

            searchPanel.Controls.AddRange(new Control[] {
                searchLabel, searchBox, clearBtn, refreshBtn
            });

            // لوحة الجدول لتجنب التداخل مع العناصر الأخرى
            var gridPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(0, 0, 0, 0),
                BackColor = Color.White
            };

            // جدول المنتجات المحسن
            var productsGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                Font = new Font("Tahoma", 12F),
                RowHeadersVisible = false,
                EnableHeadersVisualStyles = false,
                GridColor = Color.FromArgb(220, 220, 220),
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                RowTemplate = { Height = 45 }
            };

            // تنسيق رأس الجدول المحسن
            productsGrid.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            productsGrid.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            productsGrid.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            productsGrid.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            productsGrid.ColumnHeadersHeight = 50;

            // تنسيق الصفوف
            productsGrid.DefaultCellStyle.BackColor = Color.White;
            productsGrid.DefaultCellStyle.ForeColor = Color.FromArgb(52, 73, 94);
            productsGrid.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219);
            productsGrid.DefaultCellStyle.SelectionForeColor = Color.White;
            productsGrid.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            productsGrid.DefaultCellStyle.Padding = new Padding(3, 3, 3, 3);

            // تنسيق الصفوف المتناوبة
            productsGrid.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);

            // إضافة الأعمدة مع عناوين محسنة وأحجام أكبر
            var codeColumn = new DataGridViewTextBoxColumn
            {
                Name = "Code",
                HeaderText = "🏷️ كود المنتج",
                Width = 140,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Tahoma", 12F, FontStyle.Bold),
                    ForeColor = Color.FromArgb(52, 152, 219)
                }
            };

            var nameColumn = new DataGridViewTextBoxColumn
            {
                Name = "Name",
                HeaderText = "📦 اسم المنتج",
                Width = 350,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleLeft,
                    Font = new Font("Tahoma", 12F, FontStyle.Bold)
                }
            };

            var categoryColumn = new DataGridViewTextBoxColumn
            {
                Name = "Category",
                HeaderText = "📂 فئة المنتج",
                Width = 180,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Tahoma", 12F)
                }
            };

            var priceColumn = new DataGridViewTextBoxColumn
            {
                Name = "Price",
                HeaderText = "💰 السعر (ريال)",
                Width = 150,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleRight,
                    Format = "N2",
                    ForeColor = Color.FromArgb(46, 204, 113),
                    Font = new Font("Tahoma", 12F, FontStyle.Bold)
                }
            };

            var quantityColumn = new DataGridViewTextBoxColumn
            {
                Name = "Quantity",
                HeaderText = "📊 الكمية المتوفرة",
                Width = 140,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Format = "N0",
                    Font = new Font("Tahoma", 12F, FontStyle.Bold)
                }
            };

            // إضافة عمود الحالة المحسن
            var statusColumn = new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "📈 حالة المخزون",
                Width = 140,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Tahoma", 12F, FontStyle.Bold)
                }
            };

            // إضافة عمود الحد الأدنى
            var minQuantityColumn = new DataGridViewTextBoxColumn
            {
                Name = "MinQuantity",
                HeaderText = "⚠️ الحد الأدنى",
                Width = 120,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Format = "N0",
                    Font = new Font("Tahoma", 11F),
                    ForeColor = Color.FromArgb(243, 156, 18)
                }
            };

            productsGrid.Columns.AddRange(new DataGridViewColumn[] {
                codeColumn, nameColumn, categoryColumn, priceColumn, quantityColumn, statusColumn, minQuantityColumn
            });

            // تحميل المنتجات مع التحسينات
            LoadProductsToGridEnhanced(productsGrid, null);

            // لوحة الأزرار المحسنة - ارتفاع مقلل لتوفير مساحة أكبر للمنتجات
            var buttonsPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 75,
                BackColor = Color.White,
                Padding = new Padding(25, 12, 25, 12)
            };

            // إضافة حد علوي للوحة الأزرار
            buttonsPanel.Paint += (s, e) =>
            {
                var rect = new Rectangle(0, 0, buttonsPanel.Width, 1);
                e.Graphics.FillRectangle(new SolidBrush(Color.FromArgb(230, 230, 230)), rect);
            };

            var selectBtn = new Button
            {
                Text = "✅ اختيار وإضافة المنتج",
                Font = new Font("Tahoma", 11F, FontStyle.Bold),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(25, 12),
                Size = new Size(180, 42),
                Cursor = Cursors.Hand
            };
            selectBtn.FlatAppearance.BorderSize = 0;

            // إضافة تأثير hover للزر
            selectBtn.MouseEnter += (s, e) => selectBtn.BackColor = Color.FromArgb(39, 174, 96);
            selectBtn.MouseLeave += (s, e) => selectBtn.BackColor = Color.FromArgb(46, 204, 113);

            var cancelBtn = new Button
            {
                Text = "❌ إغلاق النافذة",
                Font = new Font("Tahoma", 11F, FontStyle.Bold),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(215, 12),
                Size = new Size(150, 42),
                Cursor = Cursors.Hand
            };
            cancelBtn.FlatAppearance.BorderSize = 0;

            // إضافة تأثير hover للزر
            cancelBtn.MouseEnter += (s, e) => cancelBtn.BackColor = Color.FromArgb(192, 57, 43);
            cancelBtn.MouseLeave += (s, e) => cancelBtn.BackColor = Color.FromArgb(231, 76, 60);

            var addMultipleBtn = new Button
            {
                Text = "📦 إضافة كمية مخصصة",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(375, 12),
                Size = new Size(170, 42),
                Cursor = Cursors.Hand
            };
            addMultipleBtn.FlatAppearance.BorderSize = 0;

            // معلومات المنتج المحدد المحسنة - موضع محسن
            var selectedInfoLabel = new Label
            {
                Text = "👆 اختر منتجاً من القائمة أعلاه لعرض تفاصيله هنا",
                Font = new Font("Tahoma", 9F, FontStyle.Regular),
                ForeColor = Color.FromArgb(52, 152, 219),
                Location = new Point(555, 17),
                Size = new Size(500, 32),
                TextAlign = ContentAlignment.MiddleLeft
            };

            buttonsPanel.Controls.AddRange(new Control[] {
                selectBtn, cancelBtn, addMultipleBtn, selectedInfoLabel
            });

            // الأحداث المحسنة
            searchBox.TextChanged += (s, e) => FilterProductsEnhanced(productsGrid, searchBox.Text, null);
            clearBtn.Click += (s, e) => { searchBox.Clear(); FilterProductsEnhanced(productsGrid, "", null); };
            refreshBtn.Click += (s, e) => LoadProductsToGridEnhanced(productsGrid, null);

            productsGrid.SelectionChanged += (s, e) => {
                if (productsGrid.SelectedRows.Count > 0)
                {
                    var row = productsGrid.SelectedRows[0];
                    var code = row.Cells["Code"].Value?.ToString();
                    var name = row.Cells["Name"].Value?.ToString();
                    var category = row.Cells["Category"].Value?.ToString();
                    var price = row.Cells["Price"].Value?.ToString();
                    var stock = row.Cells["Quantity"].Value?.ToString();
                    var minQty = row.Cells["MinQuantity"].Value?.ToString();
                    var status = row.Cells["Status"].Value?.ToString();

                    selectedInfoLabel.Text = $"📦 {name} | 🏷️ {code} | 📂 {category} | 💰 {price} ريال | 📊 متوفر: {stock} | ⚠️ حد أدنى: {minQty} | 📈 {status}";
                }
                else
                {
                    selectedInfoLabel.Text = "👆 اختر منتجاً من القائمة أعلاه لعرض تفاصيله هنا";
                }
            };

            productsGrid.KeyDown += (s, e) => { if (e.KeyCode == Keys.Enter) SelectProduct(); };
            productsGrid.DoubleClick += (s, e) => SelectProduct();
            selectBtn.Click += (s, e) => SelectProduct();
            cancelBtn.Click += (s, e) => searchForm.Close();

            void SelectProduct()
            {
                if (productsGrid.SelectedRows.Count > 0)
                {
                    var selectedRow = productsGrid.SelectedRows[0];
                    var productCode = selectedRow.Cells["Code"].Value?.ToString();

                    if (!string.IsNullOrEmpty(productCode))
                    {
                        SearchAndAddProduct(productCode);
                        searchForm.Close();
                    }
                }
                else
                {
                    MessageBox.Show("يرجى اختيار منتج أولاً!", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }

            // إضافة الجدول إلى لوحة الجدول
            gridPanel.Controls.Add(productsGrid);

            searchForm.Controls.AddRange(new Control[] {
                searchPanel, gridPanel, buttonsPanel
            });

            // تركيز على مربع البحث عند فتح النافذة
            searchForm.Shown += (s, e) => searchBox.Focus();

            searchForm.ShowDialog(this);
        }

        private void LoadCustomersToCombo(ComboBox combo)
        {
            try
            {
                var customers = SimpleDataManager.Instance.GetAllCustomers();
                combo.Items.Clear();

                foreach (var customer in customers.Where(c => c.IsActive))
                {
                    combo.Items.Add($"{customer.Name} - {customer.Phone}");
                }

                if (combo.Items.Count > 0)
                {
                    combo.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل العملاء: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadProductsToGrid(DataGridView grid)
        {
            try
            {
                var products = SimpleDataManager.Instance.GetAllProducts();
                grid.Rows.Clear();

                foreach (var product in products.Where(p => p.IsActive))
                {
                    grid.Rows.Add(product.Code, product.Name, product.Category,
                        product.Price, product.Quantity);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المنتجات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadProductsToGridEnhanced(DataGridView grid, Label statsLabel)
        {
            try
            {
                var products = SimpleDataManager.Instance.GetAllProducts();
                grid.Rows.Clear();

                foreach (var product in products.Where(p => p.IsActive))
                {
                    // تحديد حالة المخزون
                    string status;
                    Color statusColor;

                    if (product.Quantity <= 0)
                    {
                        status = "نفد";
                        statusColor = Color.FromArgb(231, 76, 60);
                    }
                    else if (product.Quantity <= product.MinQuantity)
                    {
                        status = "قليل";
                        statusColor = Color.FromArgb(243, 156, 18);
                    }
                    else
                    {
                        status = "متوفر";
                        statusColor = Color.FromArgb(46, 204, 113);
                    }

                    var rowIndex = grid.Rows.Add(product.Code, product.Name, product.Category,
                        product.Price, product.Quantity, status, product.MinQuantity);

                    // تلوين خلية الحالة
                    grid.Rows[rowIndex].Cells["Status"].Style.ForeColor = statusColor;

                    // تلوين الصف حسب حالة المخزون
                    if (product.Quantity <= 0)
                    {
                        grid.Rows[rowIndex].DefaultCellStyle.BackColor = Color.FromArgb(255, 235, 235);
                    }
                    else if (product.Quantity <= product.MinQuantity)
                    {
                        grid.Rows[rowIndex].DefaultCellStyle.BackColor = Color.FromArgb(255, 248, 220);
                    }
                }

                // تم حذف تحديث الإحصائيات لتوفير مساحة أكبر للمنتجات
                if (statsLabel != null)
                {
                    var totalProducts = products.Count(p => p.IsActive);
                    var outOfStock = products.Count(p => p.IsActive && p.Quantity <= 0);
                    var lowStock = products.Count(p => p.IsActive && p.Quantity > 0 && p.Quantity <= p.MinQuantity);

                    statsLabel.Text = $"إجمالي: {totalProducts} | متوفر: {totalProducts - outOfStock - lowStock} | قليل: {lowStock} | نفد: {outOfStock}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المنتجات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void FilterProductsEnhanced(DataGridView grid, string searchText, Label statsLabel)
        {
            try
            {
                var products = SimpleDataManager.Instance.GetAllProducts();
                grid.Rows.Clear();

                var filteredProducts = products.Where(p => p.IsActive &&
                    (string.IsNullOrEmpty(searchText) ||
                     p.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                     p.Code.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                     p.Category.Contains(searchText, StringComparison.OrdinalIgnoreCase)));

                foreach (var product in filteredProducts)
                {
                    // تحديد حالة المخزون
                    string status;
                    Color statusColor;

                    if (product.Quantity <= 0)
                    {
                        status = "نفد";
                        statusColor = Color.FromArgb(231, 76, 60);
                    }
                    else if (product.Quantity <= product.MinQuantity)
                    {
                        status = "قليل";
                        statusColor = Color.FromArgb(243, 156, 18);
                    }
                    else
                    {
                        status = "متوفر";
                        statusColor = Color.FromArgb(46, 204, 113);
                    }

                    var rowIndex = grid.Rows.Add(product.Code, product.Name, product.Category,
                        product.Price, product.Quantity, status, product.MinQuantity);

                    // تلوين خلية الحالة
                    grid.Rows[rowIndex].Cells["Status"].Style.ForeColor = statusColor;

                    // تلوين الصف حسب حالة المخزون
                    if (product.Quantity <= 0)
                    {
                        grid.Rows[rowIndex].DefaultCellStyle.BackColor = Color.FromArgb(255, 235, 235);
                    }
                    else if (product.Quantity <= product.MinQuantity)
                    {
                        grid.Rows[rowIndex].DefaultCellStyle.BackColor = Color.FromArgb(255, 248, 220);
                    }
                }

                // تم حذف تحديث الإحصائيات لتوفير مساحة أكبر للمنتجات
                if (statsLabel != null)
                {
                    var totalFiltered = filteredProducts.Count();
                    var outOfStock = filteredProducts.Count(p => p.Quantity <= 0);
                    var lowStock = filteredProducts.Count(p => p.Quantity > 0 && p.Quantity <= p.MinQuantity);

                    if (string.IsNullOrEmpty(searchText))
                    {
                        statsLabel.Text = $"إجمالي: {totalFiltered} | متوفر: {totalFiltered - outOfStock - lowStock} | قليل: {lowStock} | نفد: {outOfStock}";
                    }
                    else
                    {
                        statsLabel.Text = $"نتائج البحث: {totalFiltered} | متوفر: {totalFiltered - outOfStock - lowStock} | قليل: {lowStock} | نفد: {outOfStock}";
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصفية المنتجات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void FilterProducts(DataGridView grid, string searchText)
        {
            // استخدام الدالة المحسنة
            FilterProductsEnhanced(grid, searchText, null);
        }

        private void ProductsGrid_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var grid = sender as DataGridView;
                var row = grid.Rows[e.RowIndex];

                if (e.ColumnIndex == grid.Columns["Quantity"].Index ||
                    e.ColumnIndex == grid.Columns["Discount"].Index)
                {
                    CalculateRowTotal(row);
                    UpdateTotals();
                }
            }
        }

        private void ProductsGrid_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete)
            {
                var grid = sender as DataGridView;
                if (grid.SelectedRows.Count > 0)
                {
                    var result = MessageBox.Show("هل تريد حذف هذا المنتج من الفاتورة؟", "تأكيد الحذف",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        grid.Rows.RemoveAt(grid.SelectedRows[0].Index);
                        UpdateTotals();
                    }
                }
            }
        }

        private void CalculateRowTotal(DataGridViewRow row)
        {
            try
            {
                var quantity = Convert.ToDecimal(row.Cells["Quantity"].Value ?? 0);
                var price = Convert.ToDecimal(row.Cells["Price"].Value ?? 0);
                var discount = Convert.ToDecimal(row.Cells["Discount"].Value ?? 0);

                var subtotal = quantity * price;
                var discountAmount = subtotal * (discount / 100);
                var total = subtotal - discountAmount;

                row.Cells["Total"].Value = total;
            }
            catch
            {
                row.Cells["Total"].Value = 0;
            }
        }

        private void UpdateTotals()
        {
            try
            {
                var grid = contentPanel.Controls.Find("dgvProducts", true).FirstOrDefault() as DataGridView;
                if (grid == null) return;

                decimal subtotal = 0;
                foreach (DataGridViewRow row in grid.Rows)
                {
                    if (row.Cells["Total"].Value != null)
                    {
                        subtotal += Convert.ToDecimal(row.Cells["Total"].Value);
                    }
                }

                var taxRate = 0.15m; // 15% ضريبة
                var taxAmount = subtotal * taxRate;
                var total = subtotal + taxAmount;

                // تحديث التسميات مع التنسيق المحسن
                var subtotalLabel = contentPanel.Controls.Find("lblSubtotal", true).FirstOrDefault() as Label;
                var taxLabel = contentPanel.Controls.Find("lblTax", true).FirstOrDefault() as Label;
                var totalLabel = contentPanel.Controls.Find("lblTotal", true).FirstOrDefault() as Label;
                var statusLabel = contentPanel.Controls.Find("lblInvoiceStatus", true).FirstOrDefault() as Label;

                if (subtotalLabel != null) subtotalLabel.Text = $"{subtotal:N2} ريال";
                if (taxLabel != null) taxLabel.Text = $"{taxAmount:N2} ريال";
                if (totalLabel != null) totalLabel.Text = $"{total:N2} ريال";

                // تحديث حالة الفاتورة
                if (statusLabel != null)
                {
                    var itemCount = grid.Rows.Count;
                    if (itemCount == 0)
                    {
                        statusLabel.Text = "جاهز لإضافة المنتجات...";
                        statusLabel.ForeColor = Color.FromArgb(100, 100, 100);
                    }
                    else
                    {
                        statusLabel.Text = $"المنتجات: {itemCount} | المجموع: {total:N2} ريال";
                        statusLabel.ForeColor = Color.FromArgb(46, 204, 113);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حساب المجاميع: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SaveInvoice_Click(object sender, EventArgs e)
        {
            try
            {
                var grid = contentPanel.Controls.Find("dgvProducts", true).FirstOrDefault() as DataGridView;
                var customerCombo = contentPanel.Controls.Find("cmbCustomer", true).FirstOrDefault() as ComboBox;

                if (grid == null || grid.Rows.Count == 0)
                {
                    MessageBox.Show("لا توجد منتجات في الفاتورة!", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // إنشاء فاتورة جديدة
                var settings = SimpleDataManager.Instance.GetSettings();
                var newInvoiceNumber = $"INV-{settings.LastInvoiceNumber + 1:000}";

                // التحقق من اختيار عميل
                if (customerCombo == null || customerCombo.SelectedIndex < 0)
                {
                    MessageBox.Show("يرجى اختيار عميل للفاتورة!", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // الحصول على معرف العميل من النص المختار
                var selectedCustomerText = customerCombo.SelectedItem.ToString();
                var customerName = selectedCustomerText.Split(" - ")[0];
                var customers = SimpleDataManager.Instance.GetAllCustomers();
                var selectedCustomer = customers.FirstOrDefault(c => c.Name == customerName);

                if (selectedCustomer == null)
                {
                    MessageBox.Show("خطأ في العثور على بيانات العميل!", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                var invoice = new SimpleInvoice
                {
                    Id = settings.LastInvoiceNumber + 1,
                    InvoiceNumber = newInvoiceNumber,
                    CustomerId = selectedCustomer.Id,
                    CustomerName = selectedCustomer.Name,
                    Date = DateTime.Now,
                    Items = new List<SimpleInvoiceItem>()
                };

                // إضافة المنتجات
                foreach (DataGridViewRow row in grid.Rows)
                {
                    if (row.Cells["ProductName"].Value != null)
                    {
                        var item = new SimpleInvoiceItem
                        {
                            ProductId = Convert.ToInt32(row.Tag ?? 0),
                            ProductName = row.Cells["ProductName"].Value.ToString(),
                            Quantity = Convert.ToInt32(row.Cells["Quantity"].Value ?? 0),
                            Price = Convert.ToDecimal(row.Cells["Price"].Value ?? 0),
                            DiscountPercent = Convert.ToDecimal(row.Cells["Discount"].Value ?? 0),
                            Total = Convert.ToDecimal(row.Cells["Total"].Value ?? 0)
                        };
                        invoice.Items.Add(item);
                    }
                }

                // حساب المجاميع
                invoice.SubTotal = invoice.Items.Sum(i => i.Total);
                invoice.TaxAmount = invoice.SubTotal * 0.15m;
                invoice.Total = invoice.SubTotal + invoice.TaxAmount;

                // حفظ الفاتورة
                var invoices = SimpleDataManager.Instance.GetAllInvoices();
                invoices.Add(invoice);
                SimpleDataManager.Instance.SaveData("invoices", invoices);

                // تحديث رقم الفاتورة في الإعدادات
                settings.LastInvoiceNumber++;
                SimpleDataManager.Instance.SaveData("settings", settings);

                MessageBox.Show($"تم حفظ الفاتورة بنجاح!\nرقم الفاتورة: {newInvoiceNumber}", "نجح الحفظ",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                // مسح الفاتورة الحالية
                ClearCurrentInvoice();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفاتورة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PrintInvoice_Click(object sender, EventArgs e)
        {
            MessageBox.Show("ميزة الطباعة ستكون متاحة قريباً!", "قيد التطوير",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ClearInvoice_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("هل تريد مسح الفاتورة الحالية؟", "تأكيد المسح",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                ClearCurrentInvoice();
            }
        }

        private void ClearCurrentInvoice()
        {
            var grid = contentPanel.Controls.Find("dgvProducts", true).FirstOrDefault() as DataGridView;
            var customerCombo = contentPanel.Controls.Find("cmbCustomer", true).FirstOrDefault() as ComboBox;
            var customerSearchBox = contentPanel.Controls.Find("txtCustomerSearch", true).FirstOrDefault() as TextBox;
            var statusLabel = contentPanel.Controls.Find("lblInvoiceStatus", true).FirstOrDefault() as Label;

            grid?.Rows.Clear();
            if (customerCombo != null) customerCombo.SelectedIndex = -1; // عدم اختيار أي عميل
            if (customerSearchBox != null) customerSearchBox.Clear(); // مسح مربع البحث
            if (statusLabel != null) statusLabel.Text = "تم مسح الفاتورة - يرجى اختيار عميل وإضافة منتجات...";
            UpdateTotals();
        }

        private void ProductsGrid_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            var grid = sender as DataGridView;

            // التحقق من النقر على عمود الحذف
            if (e.ColumnIndex == grid.Columns["Delete"].Index && e.RowIndex >= 0)
            {
                var result = MessageBox.Show("هل تريد حذف هذا المنتج من الفاتورة؟", "تأكيد الحذف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    grid.Rows.RemoveAt(e.RowIndex);
                    UpdateTotals();

                    var statusLabel = contentPanel.Controls.Find("lblInvoiceStatus", true).FirstOrDefault() as Label;
                    if (statusLabel != null)
                        statusLabel.Text = $"تم حذف منتج - إجمالي المنتجات: {grid.Rows.Count}";
                }
            }
        }

        private void PreviewInvoice_Click(object sender, EventArgs e)
        {
            var grid = contentPanel.Controls.Find("dgvProducts", true).FirstOrDefault() as DataGridView;

            if (grid == null || grid.Rows.Count == 0)
            {
                MessageBox.Show("لا توجد منتجات في الفاتورة للمعاينة!", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            ShowInvoicePreview();
        }

        private void ShowInvoicePreview()
        {
            var previewForm = new Form
            {
                Text = "👁️ معاينة الفاتورة",
                Size = new Size(800, 600),
                StartPosition = FormStartPosition.CenterParent,
                Font = new Font("Tahoma", 10F),
                BackColor = Color.White
            };

            var previewText = GenerateInvoicePreviewText();

            var textBox = new RichTextBox
            {
                Dock = DockStyle.Fill,
                Text = previewText,
                Font = new Font("Courier New", 11F),
                ReadOnly = true,
                BackColor = Color.White,
                BorderStyle = BorderStyle.None,
                Padding = new Padding(20)
            };

            var closeBtn = new Button
            {
                Text = "❌ إغلاق",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Dock = DockStyle.Bottom,
                Height = 50,
                Cursor = Cursors.Hand
            };
            closeBtn.FlatAppearance.BorderSize = 0;
            closeBtn.Click += (s, e) => previewForm.Close();

            previewForm.Controls.AddRange(new Control[] { textBox, closeBtn });
            previewForm.ShowDialog(this);
        }

        private string GenerateInvoicePreviewText()
        {
            var grid = contentPanel.Controls.Find("dgvProducts", true).FirstOrDefault() as DataGridView;
            var customerCombo = contentPanel.Controls.Find("cmbCustomer", true).FirstOrDefault() as ComboBox;
            var subtotalLabel = contentPanel.Controls.Find("lblSubtotal", true).FirstOrDefault() as Label;
            var taxLabel = contentPanel.Controls.Find("lblTax", true).FirstOrDefault() as Label;
            var totalLabel = contentPanel.Controls.Find("lblTotal", true).FirstOrDefault() as Label;

            var preview = new System.Text.StringBuilder();

            preview.AppendLine("═══════════════════════════════════════════════════════════");
            preview.AppendLine("                    🧾 معاينة الفاتورة                    ");
            preview.AppendLine("═══════════════════════════════════════════════════════════");
            preview.AppendLine();
            preview.AppendLine($"📅 التاريخ: {DateTime.Now:yyyy/MM/dd HH:mm}");
            preview.AppendLine($"👤 العميل: {customerCombo?.SelectedItem?.ToString() ?? "لم يتم اختيار عميل"}");
            preview.AppendLine();
            preview.AppendLine("───────────────────────────────────────────────────────────");
            preview.AppendLine("المنتجات:");
            preview.AppendLine("───────────────────────────────────────────────────────────");

            if (grid != null)
            {
                foreach (DataGridViewRow row in grid.Rows)
                {
                    if (row.Cells["ProductName"].Value != null)
                    {
                        var name = row.Cells["ProductName"].Value.ToString();
                        var qty = row.Cells["Quantity"].Value.ToString();
                        var price = row.Cells["Price"].Value.ToString();
                        var total = row.Cells["Total"].Value.ToString();

                        preview.AppendLine($"📦 {name}");
                        preview.AppendLine($"   الكمية: {qty} × {price} = {total} ريال");
                        preview.AppendLine();
                    }
                }
            }

            preview.AppendLine("───────────────────────────────────────────────────────────");
            preview.AppendLine($"المجموع الفرعي: {subtotalLabel?.Text ?? "0.00"}");
            preview.AppendLine($"الضريبة %0%): {taxLabel?.Text ?? "0.00"}");
            preview.AppendLine("═══════════════════════════════════════════════════════════");
            preview.AppendLine($"💰 المجموع الكلي: {totalLabel?.Text ?? "0.00"}");
            preview.AppendLine("═══════════════════════════════════════════════════════════");

            return preview.ToString();
        }

        #endregion

        #region Customer Search Events

        private void CustomerSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                var textBox = sender as TextBox;
                var searchText = textBox?.Text.Trim();

                if (!string.IsNullOrEmpty(searchText))
                {
                    // تأثير بصري أثناء البحث
                    if (textBox != null)
                    {
                        textBox.BackColor = Color.FromArgb(255, 248, 220);
                        textBox.Enabled = false;
                    }

                    SearchAndSelectCustomerWithAnimation(searchText, textBox);
                }
                else
                {
                    // تأثير بصري للخطأ
                    if (textBox != null)
                    {
                        FlashTextBoxErrorForCustomer(textBox);
                    }
                }
            }
        }

        private void CustomerSearch_TextChanged(object sender, EventArgs e)
        {
            var textBox = sender as TextBox;
            var searchText = textBox?.Text.Trim();

            if (!string.IsNullOrEmpty(searchText) && searchText.Length >= 2)
            {
                FilterCustomersInCombo(searchText);
            }
            else if (string.IsNullOrEmpty(searchText))
            {
                // إعادة تحميل جميع العملاء عند مسح البحث
                var customerCombo = contentPanel.Controls.Find("cmbCustomer", true).FirstOrDefault() as ComboBox;
                if (customerCombo != null)
                {
                    LoadCustomersToCombo(customerCombo);
                }
            }
        }

        private void SearchCustomer_Click(object sender, EventArgs e)
        {
            ShowAdvancedCustomerSearch();
        }

        private void NewCustomer_Click(object sender, EventArgs e)
        {
            ShowNewCustomerDialog();
        }

        private void CustomerInfo_Click(object sender, EventArgs e)
        {
            ShowCustomerInfo();
        }

        private void SearchAndSelectCustomer(string searchText)
        {
            try
            {
                var customers = SimpleDataManager.Instance.GetAllCustomers();
                var foundCustomer = customers.FirstOrDefault(c => c.IsActive &&
                    (c.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                     c.Phone.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                     c.Email.Contains(searchText, StringComparison.OrdinalIgnoreCase)));

                var customerCombo = contentPanel.Controls.Find("cmbCustomer", true).FirstOrDefault() as ComboBox;
                if (customerCombo != null && foundCustomer != null)
                {
                    var customerText = $"{foundCustomer.Name} - {foundCustomer.Phone}";
                    for (int i = 0; i < customerCombo.Items.Count; i++)
                    {
                        if (customerCombo.Items[i].ToString().Contains(foundCustomer.Name))
                        {
                            customerCombo.SelectedIndex = i;
                            break;
                        }
                    }

                    // مسح مربع البحث بعد الاختيار
                    var searchBox = contentPanel.Controls.Find("txtCustomerSearch", true).FirstOrDefault() as TextBox;
                    if (searchBox != null)
                    {
                        searchBox.Clear();
                    }

                    MessageBox.Show($"تم اختيار العميل: {foundCustomer.Name}", "تم العثور على العميل",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على عميل بهذا الاسم أو الهاتف", "لم يتم العثور على نتائج",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث عن العميل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void SearchAndSelectCustomerWithAnimation(string searchText, TextBox? textBox)
        {
            try
            {
                var customers = SimpleDataManager.Instance.GetAllCustomers();
                var foundCustomer = customers.FirstOrDefault(c => c.IsActive &&
                    (c.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                     c.Phone.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                     c.Email.Contains(searchText, StringComparison.OrdinalIgnoreCase)));

                // محاكاة تأخير البحث للتأثير البصري
                await Task.Delay(300);

                var customerCombo = contentPanel.Controls.Find("cmbCustomer", true).FirstOrDefault() as ComboBox;
                if (customerCombo != null && foundCustomer != null)
                {
                    // تأثير النجاح
                    if (textBox != null)
                        textBox.BackColor = Color.FromArgb(212, 237, 218);

                    var customerText = $"{foundCustomer.Name} - {foundCustomer.Phone}";
                    for (int i = 0; i < customerCombo.Items.Count; i++)
                    {
                        if (customerCombo.Items[i].ToString().Contains(foundCustomer.Name))
                        {
                            customerCombo.SelectedIndex = i;
                            break;
                        }
                    }

                    // رسالة نجاح مؤقتة
                    ShowTemporaryMessageForCustomer($"✅ تم اختيار العميل: {foundCustomer.Name}", Color.FromArgb(46, 204, 113));

                    await Task.Delay(500);
                    if (textBox != null)
                        textBox.Clear();
                }
                else
                {
                    // تأثير الفشل
                    if (textBox != null)
                        textBox.BackColor = Color.FromArgb(248, 215, 218);

                    ShowTemporaryMessageForCustomer($"❌ لم يتم العثور على عميل: {searchText}", Color.FromArgb(231, 76, 60));
                    await Task.Delay(1000);
                }
            }
            catch (Exception ex)
            {
                if (textBox != null)
                    textBox.BackColor = Color.FromArgb(248, 215, 218);

                ShowTemporaryMessageForCustomer($"❌ خطأ في البحث: {ex.Message}", Color.FromArgb(231, 76, 60));
                await Task.Delay(1000);
            }
            finally
            {
                // إعادة تعيين حالة مربع النص
                if (textBox != null)
                {
                    textBox.BackColor = Color.White;
                    textBox.Enabled = true;
                    textBox.Focus();
                }
            }
        }

        private void FilterCustomersInCombo(string searchText)
        {
            try
            {
                var customers = SimpleDataManager.Instance.GetAllCustomers();
                var filteredCustomers = customers.Where(c => c.IsActive &&
                    (c.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                     c.Phone.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                     c.Email.Contains(searchText, StringComparison.OrdinalIgnoreCase))).ToList();

                var customerCombo = contentPanel.Controls.Find("cmbCustomer", true).FirstOrDefault() as ComboBox;
                if (customerCombo != null)
                {
                    customerCombo.Items.Clear();

                    foreach (var customer in filteredCustomers)
                    {
                        customerCombo.Items.Add($"{customer.Name} - {customer.Phone}");
                    }

                    if (customerCombo.Items.Count > 0)
                    {
                        customerCombo.SelectedIndex = 0; // اختيار أول عميل مفلتر
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصفية العملاء: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowAdvancedCustomerSearch()
        {
            var searchForm = new Form
            {
                Text = "🔍 البحث المتقدم عن العملاء",
                Size = new Size(900, 600),
                StartPosition = FormStartPosition.CenterParent,
                BackColor = Color.FromArgb(245, 245, 245),
                Font = new Font("Tahoma", 10F),
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            // شريط البحث
            var searchPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 60,
                BackColor = Color.FromArgb(52, 152, 219),
                Padding = new Padding(15)
            };

            var searchLabel = new Label
            {
                Text = "🔍 ابحث عن العميل:",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(15, 8),
                Size = new Size(150, 25)
            };

            var searchBox = new TextBox
            {
                Font = new Font("Tahoma", 12F),
                Location = new Point(15, 30),
                Size = new Size(400, 25),
                PlaceholderText = "ابحث بالاسم، الهاتف، الإيميل أو العنوان..."
            };

            var clearBtn = new Button
            {
                Text = "🗑️ مسح",
                Font = new Font("Tahoma", 11F, FontStyle.Bold),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(430, 30),
                Size = new Size(80, 25),
                Cursor = Cursors.Hand
            };
            clearBtn.FlatAppearance.BorderSize = 0;

            var refreshBtn = new Button
            {
                Text = "🔄 تحديث",
                Font = new Font("Tahoma", 11F, FontStyle.Bold),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(520, 30),
                Size = new Size(80, 25),
                Cursor = Cursors.Hand
            };
            refreshBtn.FlatAppearance.BorderSize = 0;

            searchPanel.Controls.AddRange(new Control[] { searchLabel, searchBox, clearBtn, refreshBtn });

            // جدول العملاء
            var customersGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                Font = new Font("Tahoma", 11F),
                RowHeadersVisible = false,
                EnableHeadersVisualStyles = false,
                GridColor = Color.FromArgb(230, 230, 230),
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                RowTemplate = { Height = 35 }
            };

            // تنسيق رأس الجدول
            customersGrid.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            customersGrid.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            customersGrid.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            customersGrid.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            customersGrid.ColumnHeadersHeight = 40;

            // إضافة الأعمدة
            customersGrid.Columns.AddRange(new DataGridViewColumn[] {
                new DataGridViewTextBoxColumn { Name = "Name", HeaderText = "👤 اسم العميل", Width = 200 },
                new DataGridViewTextBoxColumn { Name = "Phone", HeaderText = "📞 الهاتف", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Email", HeaderText = "📧 الإيميل", Width = 180 },
                new DataGridViewTextBoxColumn { Name = "Address", HeaderText = "📍 العنوان", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "Balance", HeaderText = "💰 الرصيد", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Status", HeaderText = "📊 الحالة", Width = 80 }
            });

            LoadCustomersToGrid(customersGrid);

            // لوحة الأزرار السفلية
            var buttonsPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 80,
                BackColor = Color.FromArgb(248, 249, 250),
                Padding = new Padding(20)
            };

            var selectBtn = new Button
            {
                Text = "✅ اختيار العميل",
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(20, 20),
                Size = new Size(150, 50),
                Cursor = Cursors.Hand
            };
            selectBtn.FlatAppearance.BorderSize = 0;

            var cancelBtn = new Button
            {
                Text = "❌ إغلاق",
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(180, 20),
                Size = new Size(100, 50),
                Cursor = Cursors.Hand
            };
            cancelBtn.FlatAppearance.BorderSize = 0;

            buttonsPanel.Controls.AddRange(new Control[] { selectBtn, cancelBtn });

            // الأحداث
            searchBox.TextChanged += (s, e) => FilterCustomersInGrid(customersGrid, searchBox.Text);
            clearBtn.Click += (s, e) => { searchBox.Clear(); FilterCustomersInGrid(customersGrid, ""); };
            refreshBtn.Click += (s, e) => LoadCustomersToGrid(customersGrid);

            selectBtn.Click += (s, e) => {
                if (customersGrid.SelectedRows.Count > 0)
                {
                    var selectedRow = customersGrid.SelectedRows[0];
                    var customerName = selectedRow.Cells["Name"].Value?.ToString();
                    var customerPhone = selectedRow.Cells["Phone"].Value?.ToString();

                    // تحديث ComboBox في الفاتورة
                    var customerCombo = contentPanel.Controls.Find("cmbCustomer", true).FirstOrDefault() as ComboBox;
                    if (customerCombo != null)
                    {
                        var customerText = $"{customerName} - {customerPhone}";
                        for (int i = 0; i < customerCombo.Items.Count; i++)
                        {
                            if (customerCombo.Items[i].ToString().Contains(customerName))
                            {
                                customerCombo.SelectedIndex = i;
                                break;
                            }
                        }
                    }

                    searchForm.Close();
                }
                else
                {
                    MessageBox.Show("يرجى اختيار عميل من القائمة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            };

            cancelBtn.Click += (s, e) => searchForm.Close();

            customersGrid.DoubleClick += (s, e) => {
                if (customersGrid.SelectedRows.Count > 0)
                {
                    selectBtn.PerformClick();
                }
            };

            searchForm.Controls.AddRange(new Control[] { buttonsPanel, customersGrid, searchPanel });
            searchForm.ShowDialog(this);
        }

        private void LoadCustomersToGrid(DataGridView grid)
        {
            try
            {
                var customers = SimpleDataManager.Instance.GetAllCustomers();
                grid.Rows.Clear();

                foreach (var customer in customers)
                {
                    var status = customer.IsActive ? "نشط" : "غير نشط";
                    var balanceText = customer.Balance >= 0 ? $"{customer.Balance:N2} ريال" : $"({Math.Abs(customer.Balance):N2}) ريال";

                    var rowIndex = grid.Rows.Add(
                        customer.Name,
                        customer.Phone,
                        customer.Email,
                        customer.Address,
                        balanceText,
                        status
                    );

                    // تلوين الصفوف حسب الحالة
                    if (!customer.IsActive)
                    {
                        grid.Rows[rowIndex].DefaultCellStyle.BackColor = Color.FromArgb(255, 240, 240);
                        grid.Rows[rowIndex].DefaultCellStyle.ForeColor = Color.Gray;
                    }
                    else if (customer.Balance < 0)
                    {
                        grid.Rows[rowIndex].DefaultCellStyle.BackColor = Color.FromArgb(255, 248, 220);
                    }

                    // حفظ معرف العميل في Tag
                    grid.Rows[rowIndex].Tag = customer.Id;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل العملاء: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void FilterCustomersInGrid(DataGridView grid, string searchText)
        {
            try
            {
                var customers = SimpleDataManager.Instance.GetAllCustomers();
                grid.Rows.Clear();

                var filteredCustomers = customers.Where(c =>
                    string.IsNullOrEmpty(searchText) ||
                    c.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                    c.Phone.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                    c.Email.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                    c.Address.Contains(searchText, StringComparison.OrdinalIgnoreCase));

                foreach (var customer in filteredCustomers)
                {
                    var status = customer.IsActive ? "نشط" : "غير نشط";
                    var balanceText = customer.Balance >= 0 ? $"{customer.Balance:N2} ريال" : $"({Math.Abs(customer.Balance):N2}) ريال";

                    var rowIndex = grid.Rows.Add(
                        customer.Name,
                        customer.Phone,
                        customer.Email,
                        customer.Address,
                        balanceText,
                        status
                    );

                    // تلوين الصفوف حسب الحالة
                    if (!customer.IsActive)
                    {
                        grid.Rows[rowIndex].DefaultCellStyle.BackColor = Color.FromArgb(255, 240, 240);
                        grid.Rows[rowIndex].DefaultCellStyle.ForeColor = Color.Gray;
                    }
                    else if (customer.Balance < 0)
                    {
                        grid.Rows[rowIndex].DefaultCellStyle.BackColor = Color.FromArgb(255, 248, 220);
                    }

                    // حفظ معرف العميل في Tag
                    grid.Rows[rowIndex].Tag = customer.Id;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصفية العملاء: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowNewCustomerDialog()
        {
            MessageBox.Show("ميزة إضافة عميل جديد ستكون متاحة قريباً!\n\nيمكنك حالياً استخدام البيانات التجريبية الموجودة.",
                "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowCustomerInfo()
        {
            var customerCombo = contentPanel.Controls.Find("cmbCustomer", true).FirstOrDefault() as ComboBox;
            if (customerCombo != null && customerCombo.SelectedIndex >= 0 && customerCombo.SelectedItem != null)
            {
                var selectedText = customerCombo.SelectedItem.ToString();
                if (selectedText != null && selectedText.Contains(" - "))
                {
                    var customerName = selectedText.Split(" - ")[0];

                    try
                    {
                        var customers = SimpleDataManager.Instance.GetAllCustomers();
                        var customer = customers.FirstOrDefault(c => c.Name == customerName);

                        if (customer != null)
                        {
                            var info = $"📋 معلومات العميل:\n\n" +
                                      $"👤 الاسم: {customer.Name}\n" +
                                      $"📞 الهاتف: {customer.Phone}\n" +
                                      $"📧 الإيميل: {customer.Email}\n" +
                                      $"📍 العنوان: {customer.Address}\n" +
                                      $"💰 الرصيد: {customer.Balance:N2} ريال\n" +
                                      $"📅 تاريخ الإنشاء: {customer.CreatedDate:dd/MM/yyyy}\n" +
                                      $"📊 الحالة: {(customer.IsActive ? "نشط" : "غير نشط")}";

                            MessageBox.Show(info, "معلومات العميل", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في عرض معلومات العميل: {ex.Message}", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار عميل أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void MainForm_Resize(object sender, EventArgs e)
        {
            // تحديد موضع زر الخروج عند تغيير حجم النافذة - محسن
            if (btnLogout != null && sidePanel.Height > 120)
            {
                btnLogout.Location = new Point(12, sidePanel.Height - 90); // موضع محسن
            }

            // إعادة تخطيط واجهة المبيعات إذا كانت مفتوحة
            if (contentPanel.Controls.Count > 0)
            {
                var salesPanel = contentPanel.Controls.OfType<Panel>().FirstOrDefault();
                if (salesPanel != null)
                {
                    // البحث عن لوحة الفاتورة الحالية
                    var invoicePanel = salesPanel.Controls.OfType<Panel>()
                        .FirstOrDefault(p => p.Controls.OfType<DataGridView>().Any(dg => dg.Name == "dgvProducts"));

                    if (invoicePanel != null)
                    {
                        // إعادة تخطيط لوحة الفاتورة
                        ResizeInvoicePanel(invoicePanel);
                    }
                }
            }
        }

        private void ResizeInvoicePanel(Panel invoicePanel)
        {
            try
            {
                // تحديث حجم لوحة الفاتورة
                var availableHeight = contentPanel.Height - 280;
                var panelHeight = Math.Max(600, availableHeight);
                invoicePanel.Size = new Size(contentPanel.Width - 40, panelHeight);

                // البحث عن العناصر وإعادة تخطيطها
                var customerPanel = invoicePanel.Controls.OfType<Panel>()
                    .FirstOrDefault(p => p.Controls.OfType<TextBox>().Any(t => t.Name == "txtCustomerSearch"));

                var productsGrid = invoicePanel.Controls.OfType<DataGridView>()
                    .FirstOrDefault(dg => dg.Name == "dgvProducts");

                var totalsPanel = invoicePanel.Controls.OfType<Panel>()
                    .FirstOrDefault(p => p.Controls.OfType<Label>().Any(l => l.Name == "lblTotal"));

                var actionsPanel = invoicePanel.Controls.OfType<Panel>()
                    .FirstOrDefault(p => p.Controls.OfType<Button>().Any());

                if (customerPanel != null)
                {
                    // تحديث عرض قسم العميل
                    customerPanel.Size = new Size(contentPanel.Width - 30, 90);
                    ResizeCustomerPanelElements(customerPanel);
                }

                if (productsGrid != null)
                {
                    // تحديث حجم وموضع جدول المنتجات
                    var gridStartY = 170;
                    var bottomSpaceNeeded = 120;
                    var gridHeight = Math.Max(300, invoicePanel.Height - gridStartY - bottomSpaceNeeded);

                    productsGrid.Location = new Point(15, gridStartY);
                    productsGrid.Size = new Size(invoicePanel.Width - 30, gridHeight);

                    // تحديث مواضع المجاميع والأزرار
                    var bottomY = gridStartY + gridHeight + 10;

                    if (totalsPanel != null)
                    {
                        totalsPanel.Location = new Point(invoicePanel.Width - 280, bottomY);
                    }

                    if (actionsPanel != null)
                    {
                        actionsPanel.Location = new Point(15, bottomY);
                    }
                }
            }
            catch (Exception ex)
            {
                // تجاهل الأخطاء في إعادة التخطيط لتجنب تعطيل الواجهة
                System.Diagnostics.Debug.WriteLine($"خطأ في إعادة تخطيط الفاتورة: {ex.Message}");
            }
        }

        private void ResizeCustomerPanelElements(Panel customerPanel)
        {
            try
            {
                var searchBox = customerPanel.Controls.OfType<TextBox>()
                    .FirstOrDefault(t => t.Name == "txtCustomerSearch");
                var combo = customerPanel.Controls.OfType<ComboBox>()
                    .FirstOrDefault(c => c.Name == "cmbCustomer");
                var buttons = customerPanel.Controls.OfType<Button>().ToArray();

                if (searchBox != null && combo != null && buttons.Length > 0)
                {
                    // إعادة حساب العرض المتاح
                    var availableWidth = customerPanel.Width - 140;
                    var searchBoxWidth = (int)(availableWidth * 0.35);
                    var comboWidth = (int)(availableWidth * 0.35);
                    var buttonWidth = Math.Max(60, (int)(availableWidth * 0.08));

                    // تحديث أحجام العناصر
                    searchBox.Size = new Size(searchBoxWidth, 25);
                    combo.Size = new Size(comboWidth, 25);

                    // تحديث مواضع الأزرار
                    var firstButtonX = 125 + Math.Max(searchBoxWidth, comboWidth) + 10;
                    var buttonSpacing = buttonWidth + 5;

                    for (int i = 0; i < buttons.Length; i++)
                    {
                        buttons[i].Location = new Point(firstButtonX + (i * buttonSpacing), 18);
                        buttons[i].Size = new Size(buttonWidth, 25);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إعادة تخطيط قسم العميل: {ex.Message}");
            }
        }

        #endregion

        #region Helper Methods

        // تم حذف التعريف المكرر لـ LoadCustomersToCombo

        private void FilterCustomers(string searchText, ComboBox combo)
        {
            try
            {
                var customers = SimpleDataManager.Instance.GetAllCustomers();
                var filteredCustomers = customers.Where(c => c.IsActive &&
                    (string.IsNullOrEmpty(searchText) ||
                     c.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                     c.Phone.Contains(searchText, StringComparison.OrdinalIgnoreCase))).ToList();

                combo.Items.Clear();
                foreach (var customer in filteredCustomers)
                {
                    combo.Items.Add($"{customer.Name} - {customer.Phone}");
                }

                if (combo.Items.Count > 0)
                {
                    combo.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث عن العملاء: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // تم حذف التعريف المكرر لـ ShowNewCustomerDialog

        private void ShowCustomerInfo(object selectedCustomer)
        {
            if (selectedCustomer != null)
            {
                MessageBox.Show($"معلومات العميل: {selectedCustomer}", "معلومات العميل",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("يرجى اختيار عميل أولاً", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void AddButtonEffects(Button button)
        {
            var originalColor = button.BackColor;

            button.MouseEnter += (s, e) => {
                button.BackColor = ControlPaint.Light(originalColor, 0.2f);
            };

            button.MouseLeave += (s, e) => {
                button.BackColor = originalColor;
            };
        }

        private void UpdateRowTotal(DataGridViewRow row)
        {
            try
            {
                var quantity = Convert.ToDecimal(row.Cells["Quantity"].Value ?? 0);
                var price = Convert.ToDecimal(row.Cells["Price"].Value ?? 0);
                var discount = Convert.ToDecimal(row.Cells["Discount"].Value ?? 0);

                var subtotal = quantity * price;
                var discountAmount = subtotal * (discount / 100);
                var total = subtotal - discountAmount;

                row.Cells["Total"].Value = total;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب مجموع الصف: {ex.Message}");
            }
        }

        // تم حذف التعريفات المكررة - الدوال الأصلية موجودة في مكان آخر

        #endregion
    }
}
