using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace SimpleAccounting
{
    public partial class TestSalesInterface : Form
    {
        public TestSalesInterface()
        {
            InitializeComponent();
            SetupForm();
            CreateTestInterface();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // TestSalesInterface
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1200, 800);
            this.Name = "TestSalesInterface";
            this.Text = "اختبار واجهة المبيعات المحسنة";
            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.Font = new Font("Tahoma", 10F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
        }

        private void CreateTestInterface()
        {
            // العنوان
            var titleLabel = new Label
            {
                Text = "🎨 اختبار واجهة المبيعات المحسنة مع الأيقونات ثلاثية الأبعاد",
                Font = new Font("Tahoma", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 20),
                Size = new Size(800, 40),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // لوحة اختبار الأيقونات
            var iconsPanel = new Panel
            {
                Location = new Point(20, 80),
                Size = new Size(1150, 200),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var iconsTitle = new Label
            {
                Text = "🔘 الأيقونات ثلاثية الأبعاد المحسنة:",
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 152, 219),
                Location = new Point(20, 10),
                Size = new Size(400, 30)
            };

            // أيقونات العمليات
            var saveBtn = Create3DIconButton("حفظ الفاتورة", "💾", Color.FromArgb(46, 204, 113),
                new Point(20, 50), new Size(150, 50));

            var printBtn = Create3DIconButton("طباعة", "🖨️", Color.FromArgb(52, 152, 219),
                new Point(180, 50), new Size(120, 50));

            var deleteBtn = Create3DIconButton("حذف", "🗑️", Color.FromArgb(231, 76, 60),
                new Point(310, 50), new Size(100, 50));

            var searchBtn = Create3DIconButton("بحث", "🔍", Color.FromArgb(155, 89, 182),
                new Point(420, 50), new Size(100, 50));

            var addBtn = Create3DIconButton("إضافة جديد", "➕", Color.FromArgb(46, 204, 113),
                new Point(530, 50), new Size(130, 50));

            var infoBtn = Create3DIconButton("معلومات", "ℹ️", Color.FromArgb(243, 156, 18),
                new Point(670, 50), new Size(120, 50));

            // أيقونات إضافية
            var settingsBtn = Create3DIconButton("إعدادات", "⚙️", Color.FromArgb(127, 140, 141),
                new Point(20, 120), new Size(120, 50));

            var reportBtn = Create3DIconButton("تقارير", "📊", Color.FromArgb(230, 126, 34),
                new Point(150, 120), new Size(120, 50));

            var customerBtn = Create3DIconButton("عملاء", "👥", Color.FromArgb(52, 152, 219),
                new Point(280, 120), new Size(100, 50));

            var productBtn = Create3DIconButton("منتجات", "📦", Color.FromArgb(46, 204, 113),
                new Point(390, 120), new Size(120, 50));

            iconsPanel.Controls.AddRange(new Control[] {
                iconsTitle, saveBtn, printBtn, deleteBtn, searchBtn, addBtn, infoBtn,
                settingsBtn, reportBtn, customerBtn, productBtn
            });

            // لوحة اختبار التخطيط
            var layoutPanel = new Panel
            {
                Location = new Point(20, 300),
                Size = new Size(1150, 450),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var layoutTitle = new Label
            {
                Text = "📍 التخطيط الجديد للمبيعات:",
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 152, 219),
                Location = new Point(20, 10),
                Size = new Size(400, 30)
            };

            // محاكاة مربع البحث السريع
            var quickSearchPanel = new Panel
            {
                Location = new Point(20, 50),
                Size = new Size(1100, 60),
                BackColor = Color.FromArgb(248, 249, 250),
                BorderStyle = BorderStyle.FixedSingle
            };

            var quickSearchLabel = new Label
            {
                Text = "🔍 البحث السريع (داخل لوحة الفاتورة):",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                Location = new Point(10, 10),
                Size = new Size(300, 25)
            };

            var quickSearchBox = new TextBox
            {
                Font = new Font("Tahoma", 12F),
                Location = new Point(10, 35),
                Size = new Size(400, 25),
                PlaceholderText = "ابحث بالباركود أو الكود..."
            };

            quickSearchPanel.Controls.AddRange(new Control[] { quickSearchLabel, quickSearchBox });

            // محاكاة التخطيط الأفقي
            var horizontalLayoutPanel = new Panel
            {
                Location = new Point(20, 130),
                Size = new Size(1100, 300),
                BackColor = Color.Transparent
            };

            // جدول المنتجات (يسار)
            var productsPanel = new Panel
            {
                Location = new Point(0, 0),
                Size = new Size(750, 280),
                BackColor = Color.FromArgb(248, 249, 250),
                BorderStyle = BorderStyle.FixedSingle
            };

            var productsLabel = new Label
            {
                Text = "📦 جدول المنتجات (عرض أكبر)",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(10, 10),
                Size = new Size(300, 25)
            };

            productsPanel.Controls.Add(productsLabel);

            // معلومات العميل (يمين)
            var customerPanel = new Panel
            {
                Location = new Point(760, 0),
                Size = new Size(340, 120),
                BackColor = Color.FromArgb(248, 249, 250),
                BorderStyle = BorderStyle.FixedSingle
            };

            var customerLabel = new Label
            {
                Text = "👤 معلومات العميل (يمين)",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(10, 10),
                Size = new Size(300, 25)
            };

            var customerSearchBtn = Create3DIconButton("بحث", "🔍", Color.FromArgb(52, 152, 219),
                new Point(10, 40), new Size(80, 30));

            var newCustomerBtn = Create3DIconButton("جديد", "➕", Color.FromArgb(46, 204, 113),
                new Point(100, 40), new Size(80, 30));

            var customerInfoBtn = Create3DIconButton("معلومات", "ℹ️", Color.FromArgb(155, 89, 182),
                new Point(190, 40), new Size(100, 30));

            customerPanel.Controls.AddRange(new Control[] {
                customerLabel, customerSearchBtn, newCustomerBtn, customerInfoBtn
            });

            // أزرار العمليات (أسفل)
            var actionsPanel = new Panel
            {
                Location = new Point(760, 130),
                Size = new Size(340, 150),
                BackColor = Color.FromArgb(248, 249, 250),
                BorderStyle = BorderStyle.FixedSingle
            };

            var actionsLabel = new Label
            {
                Text = "🔘 أزرار العمليات المحسنة:",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(10, 10),
                Size = new Size(300, 25)
            };

            var actionSaveBtn = Create3DIconButton("حفظ", "💾", Color.FromArgb(46, 204, 113),
                new Point(10, 40), new Size(75, 35));

            var actionPrintBtn = Create3DIconButton("طباعة", "🖨️", Color.FromArgb(52, 152, 219),
                new Point(95, 40), new Size(75, 35));

            var actionClearBtn = Create3DIconButton("مسح", "🗑️", Color.FromArgb(231, 76, 60),
                new Point(180, 40), new Size(75, 35));

            var actionPreviewBtn = Create3DIconButton("معاينة", "👁️", Color.FromArgb(155, 89, 182),
                new Point(265, 40), new Size(75, 35));

            actionsPanel.Controls.AddRange(new Control[] {
                actionsLabel, actionSaveBtn, actionPrintBtn, actionClearBtn, actionPreviewBtn
            });

            horizontalLayoutPanel.Controls.AddRange(new Control[] {
                productsPanel, customerPanel, actionsPanel
            });

            layoutPanel.Controls.AddRange(new Control[] {
                layoutTitle, quickSearchPanel, horizontalLayoutPanel
            });

            // إضافة جميع العناصر للنموذج
            this.Controls.AddRange(new Control[] {
                titleLabel, iconsPanel, layoutPanel
            });
        }

        // دالة إنشاء أيقونات ثلاثية الأبعاد مبسطة
        private Button Create3DIconButton(string text, string icon, Color baseColor, Point location, Size size)
        {
            var button = new Button
            {
                Text = $"{icon} {text}",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.White,
                Location = location,
                Size = size,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                UseVisualStyleBackColor = false,
                BackColor = baseColor
            };

            button.FlatAppearance.BorderSize = 0;

            // تأثيرات hover مبسطة
            button.MouseEnter += (s, e) => {
                button.BackColor = ControlPaint.Light(baseColor, 0.2f);
            };

            button.MouseLeave += (s, e) => {
                button.BackColor = baseColor;
            };

            button.MouseDown += (s, e) => {
                button.BackColor = ControlPaint.Dark(baseColor, 0.1f);
            };

            button.MouseUp += (s, e) => {
                button.BackColor = ControlPaint.Light(baseColor, 0.2f);
            };

            // إضافة حدث النقر للاختبار
            button.Click += (s, e) => {
                MessageBox.Show($"تم النقر على: {text}", "اختبار الأيقونة", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            };

            return button;
        }
    }

    // برنامج اختبار
    public static class TestProgram
    {
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new TestSalesInterface());
        }
    }
}
