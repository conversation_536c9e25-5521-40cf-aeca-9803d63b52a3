using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;

namespace SimpleAccounting
{
    public partial class CustomerDetailsForm : Form
    {
        private SimpleCustomer _customer;
        private Panel mainPanel;
        private Panel customerInfoPanel;
        private Panel invoicesPanel;
        private DataGridView invoicesGrid;
        private Label lblCustomerName;
        private Label lblTotalPurchases;
        private Label lblTotalDebt;
        private Label lblLastPurchase;
        private TextBox txtName;
        private TextBox txtPhone;
        private TextBox txtEmail;
        private TextBox txtAddress;
        private Button btnSave;
        private Button btnClose;

        public CustomerDetailsForm(int customerId)
        {
            LoadCustomerData(customerId);
            InitializeComponent();
            SetupModernUI();
            LoadCustomerInvoices();
        }

        private void LoadCustomerData(int customerId)
        {
            try
            {
                var customers = SimpleDataManager.Instance.GetAllCustomers();
                _customer = customers.FirstOrDefault(c => c.Id == customerId);
                
                if (_customer == null)
                {
                    MessageBox.Show("لم يتم العثور على العميل!", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات العميل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Close();
            }
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعدادات النافذة الأساسية
            this.Text = $"تفاصيل العميل - {_customer?.Name}";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.BackColor = Color.FromArgb(248, 250, 252);
            this.Font = new Font("Tahoma", 10F, FontStyle.Regular);
            this.MinimumSize = new Size(1000, 600);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // اللوحة الرئيسية
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(248, 250, 252),
                Padding = new Padding(20),
                AutoScroll = true
            };

            // لوحة معلومات العميل (أعلى)
            customerInfoPanel = CreateCustomerInfoPanel();
            customerInfoPanel.Dock = DockStyle.Top;
            customerInfoPanel.Height = 400; // زيادة الارتفاع لاستيعاب البطاقات

            // لوحة الفواتير (ملء المساحة المتبقية)
            invoicesPanel = CreateInvoicesPanel();
            invoicesPanel.Dock = DockStyle.Fill;

            mainPanel.Controls.AddRange(new Control[] {
                invoicesPanel, customerInfoPanel // ترتيب عكسي للـ Dock
            });

            this.Controls.Add(mainPanel);
            this.ResumeLayout(false);
        }

        private Panel CreateCustomerInfoPanel()
        {
            var panel = new Panel
            {
                BackColor = Color.White,
                Height = 400,
                Padding = new Padding(20)
            };

            // تأثير الظل
            panel.Paint += (s, e) =>
            {
                var graphics = e.Graphics;
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                // رسم ظل
                using (var shadowBrush = new SolidBrush(Color.FromArgb(30, 0, 0, 0)))
                {
                    graphics.FillRectangle(shadowBrush, 3, 3, panel.Width - 3, panel.Height - 3);
                }

                // رسم خلفية متدرجة
                using (var brush = new LinearGradientBrush(
                    new Rectangle(0, 0, panel.Width, panel.Height),
                    Color.White,
                    Color.FromArgb(250, 252, 255),
                    LinearGradientMode.Vertical))
                {
                    graphics.FillRectangle(brush, 0, 0, panel.Width - 3, panel.Height - 3);
                }

                // رسم حدود
                using (var borderPen = new Pen(Color.FromArgb(220, 220, 220), 1))
                {
                    graphics.DrawRectangle(borderPen, 0, 0, panel.Width - 1, panel.Height - 1);
                }
            };

            // العنوان
            var titleLabel = new Label
            {
                Text = "📋 معلومات العميل",
                Font = new Font("Tahoma", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 20),
                Size = new Size(300, 30),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // اسم العميل الكبير
            lblCustomerName = new Label
            {
                Text = _customer?.Name ?? "",
                Font = new Font("Tahoma", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(46, 204, 113),
                Location = new Point(20, 60),
                Size = new Size(400, 35),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // إنشاء بطاقات الملخص المالي
            CreateFinancialSummaryCards(panel);

            // حقول التعديل في الجزء السفلي
            CreateEditingFields(panel);

            panel.Controls.AddRange(new Control[] {
                titleLabel, lblCustomerName
            });

            return panel;
        }

        private Panel CreateInvoicesPanel()
        {
            var panel = new Panel
            {
                BackColor = Color.White,
                Padding = new Padding(20),
                Margin = new Padding(0, 10, 0, 0)
            };

            // تأثير الظل
            panel.Paint += (s, e) =>
            {
                var graphics = e.Graphics;
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                using (var shadowBrush = new SolidBrush(Color.FromArgb(30, 0, 0, 0)))
                {
                    graphics.FillRectangle(shadowBrush, 3, 3, panel.Width - 3, panel.Height - 3);
                }

                // رسم خلفية متدرجة
                using (var brush = new LinearGradientBrush(
                    new Rectangle(0, 0, panel.Width, panel.Height),
                    Color.White,
                    Color.FromArgb(250, 252, 255),
                    LinearGradientMode.Vertical))
                {
                    graphics.FillRectangle(brush, 0, 0, panel.Width - 3, panel.Height - 3);
                }

                using (var borderPen = new Pen(Color.FromArgb(220, 220, 220), 1))
                {
                    graphics.DrawRectangle(borderPen, 0, 0, panel.Width - 1, panel.Height - 1);
                }
            };

            // العنوان
            var titleLabel = new Label
            {
                Text = "📄 فواتير العميل",
                Font = new Font("Tahoma", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Dock = DockStyle.Top,
                Height = 40,
                TextAlign = ContentAlignment.MiddleLeft,
                Padding = new Padding(0, 10, 0, 0)
            };

            // جدول الفواتير
            invoicesGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                Font = new Font("Tahoma", 9F),
                RowHeadersVisible = false,
                EnableHeadersVisualStyles = false,
                GridColor = Color.FromArgb(230, 230, 230),
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                RowTemplate = { Height = 35 },
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            // تنسيق رأس الجدول
            invoicesGrid.ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
            {
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                Alignment = DataGridViewContentAlignment.MiddleCenter,
                SelectionBackColor = Color.FromArgb(41, 128, 185),
                SelectionForeColor = Color.White,
                Padding = new Padding(5)
            };
            invoicesGrid.ColumnHeadersHeight = 40;

            // تنسيق الصفوف
            invoicesGrid.DefaultCellStyle = new DataGridViewCellStyle
            {
                BackColor = Color.White,
                ForeColor = Color.FromArgb(52, 73, 94),
                Font = new Font("Tahoma", 9F),
                SelectionBackColor = Color.FromArgb(52, 152, 219),
                SelectionForeColor = Color.White,
                Alignment = DataGridViewContentAlignment.MiddleCenter,
                Padding = new Padding(3)
            };

            // الصفوف المتناوبة
            invoicesGrid.AlternatingRowsDefaultCellStyle = new DataGridViewCellStyle
            {
                BackColor = Color.FromArgb(248, 251, 255),
                ForeColor = Color.FromArgb(52, 73, 94),
                Font = new Font("Tahoma", 9F),
                SelectionBackColor = Color.FromArgb(52, 152, 219),
                SelectionForeColor = Color.White,
                Alignment = DataGridViewContentAlignment.MiddleCenter,
                Padding = new Padding(3)
            };

            // إضافة الأعمدة
            invoicesGrid.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "InvoiceNumber", HeaderText = "رقم الفاتورة", FillWeight = 15 },
                new DataGridViewTextBoxColumn { Name = "Date", HeaderText = "التاريخ", FillWeight = 15 },
                new DataGridViewTextBoxColumn { Name = "Time", HeaderText = "الوقت", FillWeight = 10 },
                new DataGridViewTextBoxColumn { Name = "Total", HeaderText = "المبلغ الإجمالي", FillWeight = 20 },
                new DataGridViewTextBoxColumn { Name = "Paid", HeaderText = "المبلغ المدفوع", FillWeight = 20 },
                new DataGridViewTextBoxColumn { Name = "Remaining", HeaderText = "المبلغ المتبقي", FillWeight = 20 },
                new DataGridViewTextBoxColumn { Name = "Status", HeaderText = "حالة الدفع", FillWeight = 15 }
            });

            panel.Controls.AddRange(new Control[] {
                titleLabel, invoicesGrid
            });

            return panel;
        }

        private void LoadCustomerInvoices()
        {
            try
            {
                if (_customer == null || invoicesGrid == null) return;

                var invoices = SimpleDataManager.Instance.GetAllInvoices()
                    .Where(i => i.CustomerId == _customer.Id)
                    .OrderByDescending(i => i.Date)
                    .ToList();

                invoicesGrid.Rows.Clear();

                decimal totalPurchases = 0;
                decimal totalPaid = 0;
                decimal totalDebt = 0;
                DateTime? lastPurchaseDate = null;

                foreach (var invoice in invoices)
                {
                    totalPurchases += invoice.Total;

                    // حساب المبلغ المدفوع والمتبقي (مبسط)
                    var paid = invoice.Status == "مدفوعة" ? invoice.Total : 0;
                    var remaining = invoice.Total - paid;
                    totalPaid += paid;
                    totalDebt += remaining;

                    if (lastPurchaseDate == null || invoice.Date > lastPurchaseDate)
                        lastPurchaseDate = invoice.Date;

                    var rowIndex = invoicesGrid.Rows.Add(
                        invoice.InvoiceNumber,
                        invoice.Date.ToString("dd/MM/yyyy"),
                        invoice.Date.ToString("HH:mm"),
                        $"{invoice.Total:N2} ريال",
                        $"{paid:N2} ريال",
                        $"{remaining:N2} ريال",
                        invoice.Status
                    );

                    // تلوين حسب حالة الدفع
                    var row = invoicesGrid.Rows[rowIndex];
                    if (invoice.Status == "مدفوعة")
                    {
                        row.Cells["Status"].Style.BackColor = Color.FromArgb(212, 237, 218);
                        row.Cells["Status"].Style.ForeColor = Color.FromArgb(21, 87, 36);
                    }
                    else
                    {
                        row.Cells["Status"].Style.BackColor = Color.FromArgb(248, 215, 218);
                        row.Cells["Status"].Style.ForeColor = Color.FromArgb(114, 28, 36);
                    }

                    // تلوين المبالغ المتبقية
                    if (remaining > 0)
                    {
                        row.Cells["Remaining"].Style.BackColor = Color.FromArgb(255, 243, 205);
                        row.Cells["Remaining"].Style.ForeColor = Color.FromArgb(133, 100, 4);
                    }
                }

                // تحديث البطاقات المالية إذا كانت موجودة
                UpdateFinancialCards(totalPurchases, totalPaid, totalDebt, invoices.Count, lastPurchaseDate);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل فواتير العميل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateFinancialCards(decimal totalPurchases, decimal totalPaid, decimal totalDebt, int invoiceCount, DateTime? lastPurchaseDate)
        {
            try
            {
                // البحث عن البطاقات وتحديثها
                var cardsPanel = customerInfoPanel?.Controls.OfType<Panel>().FirstOrDefault(p => p.Location.Y == 110);
                if (cardsPanel != null)
                {
                    var cards = cardsPanel.Controls.OfType<Panel>().ToList();

                    // تحديث بطاقة إجمالي المشتريات
                    if (cards.Count > 0)
                        UpdateCardValue(cards[0], $"{totalPurchases:N2} ريال");

                    // تحديث بطاقة إجمالي المدفوعات
                    if (cards.Count > 1)
                        UpdateCardValue(cards[1], $"{totalPaid:N2} ريال");

                    // تحديث بطاقة الرصيد الحالي
                    if (cards.Count > 2)
                        UpdateCardValue(cards[2], $"{_customer.Balance:N2} ريال");

                    // تحديث بطاقة عدد الفواتير
                    if (cards.Count > 3)
                        UpdateCardValue(cards[3], invoiceCount.ToString());

                    // تحديث بطاقة آخر عملية شراء
                    if (cards.Count > 4)
                    {
                        var lastPurchaseText = lastPurchaseDate?.ToString("dd/MM/yyyy") ?? "لا توجد";
                        UpdateCardValue(cards[4], lastPurchaseText);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث البطاقات المالية: {ex.Message}");
            }
        }

        private void UpdateCardValue(Panel card, string newValue)
        {
            var valueLabel = card.Controls.OfType<Label>().LastOrDefault();
            if (valueLabel != null)
            {
                valueLabel.Text = newValue;
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم العميل!", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return;
                }

                // تحديث بيانات العميل
                _customer.Name = txtName.Text.Trim();
                _customer.Phone = txtPhone.Text.Trim();
                _customer.Email = txtEmail.Text.Trim();
                _customer.Address = txtAddress.Text.Trim();

                // حفظ التغييرات
                var customers = SimpleDataManager.Instance.GetAllCustomers();
                var customerIndex = customers.FindIndex(c => c.Id == _customer.Id);
                if (customerIndex >= 0)
                {
                    customers[customerIndex] = _customer;
                    SimpleDataManager.Instance.SaveData("customers", customers);

                    // تحديث العرض
                    lblCustomerName.Text = _customer.Name;
                    this.Text = $"تفاصيل العميل - {_customer.Name}";

                    MessageBox.Show("تم حفظ التغييرات بنجاح!", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التغييرات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CreateFinancialSummaryCards(Panel parentPanel)
        {
            // حساب القيم المالية
            var invoices = SimpleDataManager.Instance.GetAllInvoices()
                .Where(i => i.CustomerId == _customer.Id).ToList();

            decimal totalPurchases = invoices.Sum(i => i.Total);
            decimal totalPaid = invoices.Where(i => i.Status == "مدفوعة").Sum(i => i.Total);
            decimal currentBalance = _customer.Balance;
            int invoiceCount = invoices.Count;
            var lastPurchaseDate = invoices.OrderByDescending(i => i.Date).FirstOrDefault()?.Date;

            // إنشاء البطاقات
            var cardsPanel = new Panel
            {
                Location = new Point(20, 110),
                Size = new Size(parentPanel.Width - 40, 100),
                BackColor = Color.Transparent
            };

            // بطاقة إجمالي المشتريات
            var purchasesCard = CreateInfoCard("إجمالي المشتريات", $"{totalPurchases:N2} ريال", "💰",
                Color.FromArgb(46, 204, 113), new Point(0, 0));

            // بطاقة إجمالي المدفوعات
            var paymentsCard = CreateInfoCard("إجمالي المدفوعات", $"{totalPaid:N2} ريال", "✅",
                Color.FromArgb(155, 89, 182), new Point(200, 0));

            // بطاقة الرصيد الحالي
            var balanceCard = CreateInfoCard("الرصيد الحالي", $"{currentBalance:N2} ريال", "📊",
                Color.FromArgb(52, 152, 219), new Point(400, 0));

            // بطاقة عدد الفواتير
            var invoicesCard = CreateInfoCard("عدد الفواتير", invoiceCount.ToString(), "📄",
                Color.FromArgb(230, 126, 34), new Point(600, 0));

            // بطاقة آخر عملية شراء
            var lastPurchaseText = lastPurchaseDate?.ToString("dd/MM/yyyy") ?? "لا توجد";
            var lastPurchaseCard = CreateInfoCard("آخر عملية شراء", lastPurchaseText, "📅",
                Color.FromArgb(231, 76, 60), new Point(800, 0));

            cardsPanel.Controls.AddRange(new Control[] {
                purchasesCard, paymentsCard, balanceCard, invoicesCard, lastPurchaseCard
            });

            parentPanel.Controls.Add(cardsPanel);

            // حفظ المراجع للتحديث لاحقاً
            lblTotalPurchases = purchasesCard.Controls.OfType<Label>().LastOrDefault();
            lblTotalDebt = balanceCard.Controls.OfType<Label>().LastOrDefault();
            lblLastPurchase = lastPurchaseCard.Controls.OfType<Label>().LastOrDefault();
        }

        private Panel CreateInfoCard(string title, string value, string icon, Color color, Point location)
        {
            var card = new Panel
            {
                Size = new Size(180, 80),
                Location = location,
                BackColor = Color.White,
                BorderStyle = BorderStyle.None
            };

            // تأثيرات ثلاثية الأبعاد للبطاقة
            card.Paint += (s, e) => {
                var graphics = e.Graphics;
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                // رسم ظل
                using (var shadowBrush = new SolidBrush(Color.FromArgb(30, 0, 0, 0)))
                {
                    graphics.FillRectangle(shadowBrush, 2, 2, card.Width - 2, card.Height - 2);
                }

                // رسم خلفية متدرجة
                using (var brush = new LinearGradientBrush(
                    card.ClientRectangle,
                    Color.White,
                    Color.FromArgb(250, 252, 255),
                    LinearGradientMode.Vertical))
                {
                    graphics.FillRectangle(brush, card.ClientRectangle);
                }

                // رسم حد ملون
                using (var pen = new Pen(color, 3))
                {
                    graphics.DrawLine(pen, 0, 0, card.Width, 0);
                }

                // رسم حدود
                using (var borderPen = new Pen(Color.FromArgb(220, 220, 220), 1))
                {
                    graphics.DrawRectangle(borderPen, 0, 0, card.Width - 1, card.Height - 1);
                }
            };

            var iconLabel = new Label
            {
                Text = icon,
                Font = new Font("Segoe UI Emoji", 16F),
                ForeColor = color,
                Size = new Size(40, 40),
                Location = new Point(10, 10),
                TextAlign = ContentAlignment.MiddleCenter
            };

            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Tahoma", 8F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Size = new Size(120, 20),
                Location = new Point(55, 15),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var valueLabel = new Label
            {
                Text = value,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = color,
                Size = new Size(120, 25),
                Location = new Point(55, 40),
                TextAlign = ContentAlignment.MiddleLeft
            };

            card.Controls.AddRange(new Control[] { iconLabel, titleLabel, valueLabel });

            return card;
        }

        private void CreateEditingFields(Panel parentPanel)
        {
            // حقول التعديل
            var fieldsY = 230;

            var nameLabel = new Label
            {
                Text = "الاسم:",
                Font = new Font("Tahoma", 11F, FontStyle.Bold),
                Location = new Point(20, fieldsY),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            txtName = new TextBox
            {
                Text = _customer?.Name ?? "",
                Font = new Font("Tahoma", 11F),
                Location = new Point(110, fieldsY - 2),
                Size = new Size(200, 25),
                BorderStyle = BorderStyle.FixedSingle
            };

            var phoneLabel = new Label
            {
                Text = "الهاتف:",
                Font = new Font("Tahoma", 11F, FontStyle.Bold),
                Location = new Point(330, fieldsY),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            txtPhone = new TextBox
            {
                Text = _customer?.Phone ?? "",
                Font = new Font("Tahoma", 11F),
                Location = new Point(420, fieldsY - 2),
                Size = new Size(200, 25),
                BorderStyle = BorderStyle.FixedSingle
            };

            fieldsY += 40;

            var emailLabel = new Label
            {
                Text = "البريد:",
                Font = new Font("Tahoma", 11F, FontStyle.Bold),
                Location = new Point(20, fieldsY),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            txtEmail = new TextBox
            {
                Text = _customer?.Email ?? "",
                Font = new Font("Tahoma", 11F),
                Location = new Point(110, fieldsY - 2),
                Size = new Size(200, 25),
                BorderStyle = BorderStyle.FixedSingle
            };

            var addressLabel = new Label
            {
                Text = "العنوان:",
                Font = new Font("Tahoma", 11F, FontStyle.Bold),
                Location = new Point(330, fieldsY),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            txtAddress = new TextBox
            {
                Text = _customer?.Address ?? "",
                Font = new Font("Tahoma", 11F),
                Location = new Point(420, fieldsY - 2),
                Size = new Size(200, 25),
                BorderStyle = BorderStyle.FixedSingle
            };

            fieldsY += 50;

            // أزرار الحفظ والإغلاق
            btnSave = new Button
            {
                Text = "💾 حفظ التغييرات",
                Font = new Font("Tahoma", 11F, FontStyle.Bold),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(110, fieldsY),
                Size = new Size(150, 35),
                Cursor = Cursors.Hand
            };
            btnSave.FlatAppearance.BorderSize = 0;

            btnClose = new Button
            {
                Text = "❌ إغلاق",
                Font = new Font("Tahoma", 11F, FontStyle.Bold),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(270, fieldsY),
                Size = new Size(100, 35),
                Cursor = Cursors.Hand
            };
            btnClose.FlatAppearance.BorderSize = 0;

            // إضافة الأحداث
            btnSave.Click += BtnSave_Click;
            btnClose.Click += (s, e) => this.Close();

            parentPanel.Controls.AddRange(new Control[] {
                nameLabel, txtName, phoneLabel, txtPhone,
                emailLabel, txtEmail, addressLabel, txtAddress,
                btnSave, btnClose
            });
        }

        private void SetupModernUI()
        {
            this.SetStyle(ControlStyles.AllPaintingInWmPaint |
                         ControlStyles.UserPaint |
                         ControlStyles.DoubleBuffer, true);
        }
    }
}
